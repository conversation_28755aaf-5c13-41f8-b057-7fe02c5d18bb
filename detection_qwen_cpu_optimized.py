#!/usr/bin/env python3
"""
EXTRACTEUR BTK AVEC QWEN 2.5 OPTIMISÉ POUR CPU
Version haute performance sur processeur avec Qwen2.5-1.5B-Instruct
"""

import os
import json
import pytesseract
from pathlib import Path
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Any, Tuple
from dotenv import load_dotenv
from bs4 import BeautifulSoup

# Imports pour Qwen optimisé CPU
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False
    logging.warning("⚠️ Transformers non disponible")

# Configuration
load_dotenv()
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Dossiers
PDF_FOLDER = 'contrat'
IMG_FOLDER = 'images'
TEXT_FOLDER = 'ocr_texts'
RESULT_FOLDER = 'results'

class QwenCPUOptimizedExtractor:
    """Extracteur BTK optimisé pour CPU avec Qwen 2.5"""
    
    def __init__(self):
        # Configuration OCR optimisée
        self.ocr_config = r'--oem 3 --psm 6 -l fra+eng+ara'
        
        # Champs obligatoires étendus
        self.required_fields = {
            "informations_banque": [
                "nom_banque", "capital_social", "numero_rc", "adresse", 
                "forme_juridique", "telephone"
            ],
            "informations_contrat": [
                "type_contrat", "numero_contrat", "date_edition", "date_signature",
                "montant_principal", "duree", "taux_interet", "taux_semestriel",
                "taux_effectif_global", "commission_gestion", "commission_engagement", 
                "garanties"
            ],
            "informations_client": [
                "code_client", "nom_client", "numero_cin", "adresse_client",
                "secteur_activite", "numero_rc_client", "profession"
            ],
            "informations_signatures": [
                "date_signature", "lieu_signature", "representant_banque"
            ]
        }
        
        # Initialisation du modèle Qwen optimisé CPU
        self.qwen_model = None
        self.qwen_tokenizer = None
        self.init_qwen_cpu_optimized()

    def init_qwen_cpu_optimized(self):
        """Initialise Qwen 2.5 optimisé pour CPU"""
        if not QWEN_AVAILABLE:
            logger.warning("⚠️ Qwen non disponible - utilisation OCR seul")
            return
            
        try:
            logger.info("🧠 Chargement Qwen 2.5-1.5B optimisé CPU...")
            
            # Modèle optimal pour CPU : équilibre performance/vitesse
            model_name = "Qwen/Qwen2.5-1.5B-Instruct"
            
            logger.info(f"🔧 Chargement sur CPU avec optimisations...")
            
            # Tokenizer avec optimisations
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                model_name, 
                trust_remote_code=True,
                use_fast=True  # Tokenizer rapide
            )
            
            # Modèle avec optimisations CPU
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float32,  # Float32 optimal pour CPU
                device_map=None,  # Pas de device_map pour CPU
                trust_remote_code=True,
                low_cpu_mem_usage=True,
                use_cache=True  # Cache pour accélérer
            )
            
            # Optimisations CPU
            self.qwen_model = self.qwen_model.to("cpu")
            self.qwen_model.eval()  # Mode évaluation pour performance
            
            # Optimisations PyTorch pour CPU
            torch.set_num_threads(4)  # Utiliser 4 threads CPU
            
            logger.info("✅ Qwen 2.5-1.5B chargé et optimisé pour CPU!")
            
        except Exception as e:
            logger.error(f"❌ Erreur chargement Qwen: {str(e)}")
            self.qwen_model = None
            self.qwen_tokenizer = None

    def enhance_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """Amélioration d'image optimisée pour OCR"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage léger
        denoised = cv2.medianBlur(img_array, 3)
        
        # Amélioration contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation Otsu
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return Image.fromarray(binary)

    def extract_text_fast(self, pdf_path: str, pdf_name: str) -> str:
        """Extraction de texte rapide et efficace"""
        logger.info(f"📄 Extraction OCR rapide pour {pdf_name}")
        
        # Créer les dossiers
        for folder in [IMG_FOLDER, TEXT_FOLDER]:
            os.makedirs(folder, exist_ok=True)
        
        # Conversion PDF avec DPI modéré pour vitesse
        try:
            pages = convert_from_path(pdf_path, dpi=200, fmt='png')
        except Exception as e:
            logger.error(f"❌ Erreur conversion PDF: {str(e)}")
            return ""
        
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Page {page_num}...")
            
            try:
                # Amélioration rapide
                enhanced_page = self.enhance_image_for_ocr(page)
                
                # OCR rapide
                text = pytesseract.image_to_string(enhanced_page, config=self.ocr_config)
                all_text += f"\n--- Page {page_num} ---\n{text}"
                
                logger.info(f"✅ Page {page_num} traitée")
            except Exception as e:
                logger.error(f"❌ Erreur page {page_num}: {str(e)}")
        
        # Sauvegarde du texte
        text_path = f"{TEXT_FOLDER}/{pdf_name}.txt"
        try:
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(all_text)
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde: {str(e)}")
        
        return all_text

    def extract_with_advanced_patterns(self, text: str) -> Dict:
        """Extraction avec patterns avancés"""
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_signatures": {}
        }
        
        # Nettoyage du texte
        clean_text = re.sub(r'\s+', ' ', text)
        
        # Patterns ultra-optimisés
        patterns = {
            "informations_banque": {
                "nom_banque": [
                    r"(?:La\s+)?BANQUE\s+TUNISO[\-\s]*KOWEITIENNE",
                    r"B\.T\.K\.?",
                    r"(?<![\w])BTK(?![\w])"
                ],
                "capital_social": [
                    r"capital\s+de\s+([\d\s,\.]+)\s*dinars?",
                    r"au\s+capital\s+de\s+([\d\s,\.]+)"
                ],
                "numero_rc": [
                    r"R\.?C\.?\s*[=:]\s*([A-Z]?\d+)",
                    r"registre\s+(?:de\s+)?commerce\s*[=:]\s*([A-Z]?\d+)"
                ],
                "adresse": [
                    r"siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)",
                    r"(\d+\s*bis[,\s]+Avenue\s+[^,\n\.]+)"
                ],
                "forme_juridique": [
                    r"(société\s+anonyme)",
                    r"(S\.A\.?)"
                ],
                "telephone": [
                    r"(?:Tél|Tel|Téléphone)\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})"
                ]
            },
            "informations_contrat": {
                "type_contrat": [
                    r"CONTRAT\s+DE\s+(PRET|PRÊT)",
                    r"contrat\s+de\s+(crédit|prêt)"
                ],
                "numero_contrat": [
                    r"Numéro\s+(?:de\s+)?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)",
                    r"Référence\s*[=:]\s*([A-Z0-9\-\/]+)"
                ],
                "date_edition": [
                    r"Edité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"Date\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "date_signature": [
                    r"signé\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"fait\s+le\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "montant_principal": [
                    r"somme\s+principale\s+(?:de\s+)?([\d\s,\.]+)",
                    r"montant\s+(?:du\s+)?(?:crédit|prêt)\s*[=:]\s*([\d\s,\.]+)"
                ],
                "duree": [
                    r"durée\s+de\s+(\d+\s+mois)",
                    r"pour\s+une\s+durée\s+de\s+(\d+\s+mois)"
                ],
                "taux_interet": [
                    r"taux\s+d['\']intérêt\s*[=:]\s*([\d,\.]+\s*%?)",
                    r"intérêts\s+au\s+taux\s+(?:de\s+)?([\d,\.]+\s*%)"
                ],
                "taux_semestriel": [
                    r"taux\s+semestriel\s+(?:du\s+marché\s+monétaire\s+)?(?:majoré\s+de\s+)?([\d,\.]+\s*%?)"
                ],
                "taux_effectif_global": [
                    r"taux\s+effectif\s+global\s*[=:]\s*([\d,\.]+\s*%)",
                    r"T\.?E\.?G\.?\s*[=:]\s*([\d,\.]+\s*%)"
                ],
                "commission_gestion": [
                    r"commission\s+de\s+gestion\s+de\s+([\d,\.]+\s*%)"
                ],
                "commission_engagement": [
                    r"commission\s+d['\']engagement\s*[=:]\s*([\d,\.]+\s*%)"
                ],
                "garanties": [
                    r"garanties?\s*[=:]\s*([^\.]+)",
                    r"hypothèque\s+sur\s+([^\.]+)"
                ]
            },
            "informations_client": {
                "code_client": [
                    r"Code\s+Client\s*[=:]\s*([A-Z0-9X]{4,})"
                ],
                "nom_client": [
                    r"Et\s+([A-Z][A-Z\s&\-\.]+),?\s+dont\s+le\s+siège",
                    r"Emprunteuse?\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)"
                ],
                "numero_cin": [
                    r"CIN\s*[=:]\s*(\d{8})",
                    r"C\.I\.N\.?\s*[=:]\s*(\d{8})"
                ],
                "adresse_client": [
                    r"dont\s+le\s+siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)"
                ],
                "numero_rc_client": [
                    r"immatriculé(?:e)?\s+au\s+(?:Centre\s+National\s+du\s+)?Registre\s+(?:des\s+Entreprises\s+)?sous\s+(?:l['\']identifiant\s+unique\s+)?n°\s*([A-Z0-9]+)"
                ],
                "secteur_activite": [
                    r"Secteur\s+d['\']activité\s*[=:]\s*([^,\n\.]+)"
                ],
                "profession": [
                    r"profession\s*[=:]\s*([^,\n\.]+)"
                ]
            },
            "informations_signatures": {
                "date_signature": [
                    r"signé\s+le\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"fait\s+le\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "lieu_signature": [
                    r"signé\s+à\s+([^,\n\.]+)",
                    r"fait\s+à\s+([^,\n\.]+)"
                ],
                "representant_banque": [
                    r"représentant\s*[=:]\s*([A-Z][A-Z\s\-\.]+)"
                ]
            }
        }
        
        # Application des patterns
        for section, section_patterns in patterns.items():
            for field, field_patterns in section_patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, clean_text, re.IGNORECASE)
                    if match:
                        value = match.group(1).strip() if match.groups() else match.group(0).strip()
                        if value and not result[section].get(field):
                            result[section][field] = value
                            break
        
        return result

    def call_qwen_cpu_optimized(self, text: str) -> Dict:
        """Appel Qwen optimisé pour CPU avec prompt efficace"""
        if not self.qwen_model or not self.qwen_tokenizer:
            logger.warning("⚠️ Modèle Qwen non disponible")
            return {}

        logger.info("🧠 Analyse Qwen CPU optimisée...")

        try:
            # Prompt optimisé pour Qwen 2.5 sur CPU
            prompt = f"""<|im_start|>system
Tu es un expert en analyse de contrats bancaires BTK. Extrait précisément les informations au format JSON.
<|im_end|>

<|im_start|>user
Analyse ce contrat BTK et extrait les informations au format JSON strict :

STRUCTURE ATTENDUE :
{{
  "informations_banque": {{
    "nom_banque": "",
    "capital_social": "",
    "numero_rc": "",
    "adresse": "",
    "forme_juridique": "",
    "telephone": ""
  }},
  "informations_contrat": {{
    "type_contrat": "",
    "numero_contrat": "",
    "date_edition": "",
    "date_signature": "",
    "montant_principal": "",
    "duree": "",
    "taux_interet": "",
    "taux_semestriel": "",
    "taux_effectif_global": "",
    "commission_gestion": "",
    "commission_engagement": "",
    "garanties": ""
  }},
  "informations_client": {{
    "code_client": "",
    "nom_client": "",
    "numero_cin": "",
    "adresse_client": "",
    "secteur_activite": "",
    "numero_rc_client": "",
    "profession": ""
  }},
  "informations_signatures": {{
    "date_signature": "",
    "lieu_signature": "",
    "representant_banque": ""
  }}
}}

RÈGLES :
- Extraire EXACTEMENT du texte
- CIN : 8 chiffres
- Dates : JJ/MM/AAAA
- Conserver valeurs masquées (xxx, XXXX)
- JSON valide uniquement

TEXTE DU CONTRAT :
{text[:8000]}
<|im_end|>

<|im_start|>assistant
```json"""

            # Tokenisation optimisée pour CPU
            inputs = self.qwen_tokenizer(
                prompt,
                return_tensors="pt",
                max_length=4096,
                truncation=True,
                padding=False  # Pas de padding pour CPU
            )

            # Génération optimisée CPU
            with torch.no_grad():
                outputs = self.qwen_model.generate(
                    inputs.input_ids,
                    max_new_tokens=1000,  # Limité pour vitesse CPU
                    temperature=0.1,      # Basse température pour précision
                    do_sample=True,
                    top_p=0.9,
                    repetition_penalty=1.05,
                    pad_token_id=self.qwen_tokenizer.eos_token_id,
                    eos_token_id=self.qwen_tokenizer.eos_token_id,
                    use_cache=True  # Cache pour vitesse
                )

            # Décodage
            response = self.qwen_tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )

            # Extraction JSON
            json_match = re.search(r'\{[\s\S]*\}', response)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    logger.info("✅ Analyse Qwen CPU réussie")
                    return result
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Erreur JSON: {str(e)}")

        except Exception as e:
            logger.error(f"❌ Erreur Qwen CPU: {str(e)}")

        return {}

    def merge_results_smart(self, regex_result: Dict, qwen_result: Dict) -> Dict:
        """Fusion intelligente des résultats regex et Qwen"""
        merged = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_signatures": {}
        }

        for section in merged.keys():
            # Commencer avec regex
            merged[section] = regex_result.get(section, {}).copy()

            # Ajouter/améliorer avec Qwen
            qwen_section = qwen_result.get(section, {})
            for field, value in qwen_section.items():
                if value and value.strip():
                    # Qwen a priorité si la valeur est plus complète
                    current_value = merged[section].get(field, "")
                    if not current_value or len(str(value)) > len(str(current_value)):
                        merged[section][field] = value

        return merged

    def calculate_scores_fast(self, results: Dict) -> Dict:
        """Calcul rapide des scores de qualité"""
        scores = {
            "score_global": 0,
            "completude_banque": 0,
            "completude_contrat": 0,
            "completude_client": 0,
            "completude_signatures": 0
        }

        # Calcul par section
        for section, required in self.required_fields.items():
            section_key = f"completude_{section.split('_')[1]}"
            found_fields = len([f for f in required if results.get(section, {}).get(f)])
            scores[section_key] = (found_fields / len(required)) * 100

        # Score global pondéré
        scores["score_global"] = (
            scores["completude_banque"] * 0.2 +
            scores["completude_contrat"] * 0.4 +
            scores["completude_client"] * 0.3 +
            scores["completude_signatures"] * 0.1
        )

        return scores

    def process_contract_cpu_optimized(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement optimisé pour CPU"""
        logger.info(f"🚀 TRAITEMENT CPU OPTIMISÉ: {pdf_name}")

        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_signatures": {},
            "scores_qualite": {}
        }

        try:
            # 1. Extraction OCR rapide
            logger.info("📄 Phase 1: Extraction OCR rapide...")
            ocr_text = self.extract_text_fast(pdf_path, pdf_name)

            if not ocr_text.strip():
                raise Exception("Aucun texte extrait")

            logger.info(f"📝 Texte extrait: {len(ocr_text)} caractères")

            # 2. Extraction patterns avancés
            logger.info("🔍 Phase 2: Patterns avancés...")
            regex_results = self.extract_with_advanced_patterns(ocr_text)

            # 3. Analyse Qwen CPU optimisée
            qwen_results = {}
            if self.qwen_model:
                logger.info("🧠 Phase 3: Qwen CPU optimisé...")
                qwen_results = self.call_qwen_cpu_optimized(ocr_text)
            else:
                logger.info("⚠️ Phase 3: Qwen non disponible")

            # 4. Fusion intelligente
            logger.info("🔄 Phase 4: Fusion intelligente...")
            final_results = self.merge_results_smart(regex_results, qwen_results)

            # 5. Calcul des scores
            quality_scores = self.calculate_scores_fast(final_results)

            # 6. Assemblage final
            result.update(final_results)
            result["scores_qualite"] = quality_scores

            # 7. Métadonnées
            result['metadata'] = {
                'fichier_source': f"{pdf_name}.pdf",
                'extraction_timestamp': datetime.now().isoformat(),
                'methode_extraction': 'Qwen 2.5 CPU Optimized',
                'ocr_text_length': len(ocr_text),
                'qwen_available': self.qwen_model is not None,
                'quality_score_global': quality_scores.get('score_global', 0)
            }

            # 8. Affichage résumé
            self.display_summary_fast(result)

            logger.info(f"✅ TRAITEMENT CPU TERMINÉ!")
            return result

        except Exception as e:
            logger.error(f"❌ Erreur traitement {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf",
                "informations_banque": {},
                "informations_contrat": {},
                "informations_client": {},
                "informations_signatures": {},
                "scores_qualite": {"score_global": 0}
            }

    def display_summary_fast(self, result: Dict):
        """Affichage rapide du résumé"""
        logger.info("📊 === RÉSUMÉ EXTRACTION ===")

        for section in ["informations_banque", "informations_contrat", "informations_client", "informations_signatures"]:
            section_name = section.replace("informations_", "").upper()
            found = len([v for v in result.get(section, {}).values() if v])
            total = len(self.required_fields.get(section, []))
            logger.info(f"📋 {section_name}: {found}/{total} champs")

        quality = result.get("scores_qualite", {}).get("score_global", 0)
        logger.info(f"⭐ Score global: {quality:.1f}%")

def main():
    """Fonction principale optimisée"""
    logger.info("🎯 === EXTRACTEUR BTK QWEN 2.5 CPU ===")

    # Initialisation
    extractor = QwenCPUOptimizedExtractor()

    # Vérification dossiers
    if not os.path.exists(PDF_FOLDER):
        logger.error(f"❌ Dossier {PDF_FOLDER} non trouvé")
        return

    # Création dossiers sortie
    for folder in [IMG_FOLDER, TEXT_FOLDER, RESULT_FOLDER]:
        os.makedirs(folder, exist_ok=True)

    # Recherche PDFs
    pdf_files = [f for f in os.listdir(PDF_FOLDER)
                 if f.lower().endswith('.pdf') and not f.startswith('~')]

    if not pdf_files:
        logger.error(f"❌ Aucun PDF dans {PDF_FOLDER}")
        return

    logger.info(f"📁 {len(pdf_files)} fichier(s) PDF trouvé(s)")

    # Traitement
    total_quality = 0
    successful = 0

    for i, pdf_file in enumerate(pdf_files, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        logger.info(f"{'='*60}")

        pdf_path = os.path.join(PDF_FOLDER, pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]

        # Traitement
        result = extractor.process_contract_cpu_optimized(pdf_path, pdf_name)

        # Sauvegarde
        output_path = f"{RESULT_FOLDER}/{pdf_name}_QWEN25_CPU.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Sauvegardé: {output_path}")

        # Stats
        if "erreur" not in result:
            successful += 1
            total_quality += result.get("scores_qualite", {}).get("score_global", 0)

    # Résumé final
    logger.info(f"\n{'='*60}")
    logger.info("🏁 EXTRACTION TERMINÉE!")
    logger.info(f"✅ Succès: {successful}/{len(pdf_files)}")
    if successful > 0:
        avg_quality = total_quality / successful
        logger.info(f"⭐ Qualité moyenne: {avg_quality:.1f}%")
    logger.info(f"{'='*60}")

if __name__ == "__main__":
    main()
