#!/usr/bin/env python3
"""
EXTRACTEUR DE CONTRATS BANCAIRES AVANCÉ AVEC QWEN
Version optimisée combinant OCR + Qwen pour une précision maximale
"""

import os
import json
import pytesseract
from pathlib import Path
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
import time
from typing import Dict, List, Any, Tuple
from dotenv import load_dotenv
from bs4 import BeautifulSoup

# Imports pour Qwen
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
    import torch
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False
    logging.warning("⚠️ Transformers non disponible - Qwen désactivé")

# Charger les variables d'environnement
load_dotenv()

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Dossiers
PDF_FOLDER = 'contrat'
IMG_FOLDER = 'images'
HOCR_FOLDER = 'hocr'
RESULT_FOLDER = 'results'
TEXT_FOLDER = 'ocr_texts'

class QwenBTKContractExtractor:
    """Extracteur spécialisé pour contrats BTK avec Qwen"""
    
    def __init__(self):
        # Configuration OCR
        self.ocr_config = r'--oem 3 --psm 6 -l fra+eng+ara'
        
        # Champs obligatoires spécifiques BTK
        self.required_fields = {
            "informations_banque": [
                "nom_banque", "capital_social", "numero_rc", "adresse", 
                "forme_juridique", "telephone"
            ],
            "informations_contrat": [
                "type_contrat", "numero_contrat", "date_edition", 
                "montant_principal", "duree", "taux_interet", 
                "commission_gestion", "garanties"
            ],
            "informations_client": [
                "code_client", "nom_client", "adresse_client", 
                "secteur_activite", "numero_rc_client"
            ]
        }
        
        # Mapping des champs avec variations
        self.field_mapping = {
            "nom_banque": ["BANQUE TUNISO-KOWEITIENNE", "BTK", "B.T.K"],
            "capital_social": ["capital", "capital social", "capital de"],
            "numero_rc": ["R.C.", "RC", "Registre de Commerce", "registre commercial"],
            "adresse": ["siège social", "adresse", "domicilié"],
            "type_contrat": ["CONTRAT DE PRET", "CONTRAT DE PRÊT", "prêt", "crédit"],
            "numero_contrat": ["Numéro", "Référence", "N°", "numéro contrat"],
            "date_edition": ["Edité le", "édité le", "date", "le"],
            "montant_principal": ["somme principale", "montant", "principal"],
            "duree": ["durée", "durée de", "pour une durée"],
            "taux_interet": ["taux", "intérêts", "taux semestriel"],
            "commission_gestion": ["commission de gestion", "commission"],
            "code_client": ["Code Client", "Code", "Identifiant Client"],
            "nom_client": ["Nom du Client", "Client", "Emprunteuse", "Souscripteur"],
            "secteur_activite": ["Secteur d'activité", "activité", "secteur"]
        }
        
        # Initialisation du modèle Qwen
        self.qwen_model = None
        self.qwen_tokenizer = None
        self.init_qwen_model()

    def init_qwen_model(self):
        """Initialise le modèle Qwen pour l'analyse de contrats"""
        if not QWEN_AVAILABLE:
            logger.warning("⚠️ Qwen non disponible - utilisation OCR seul")
            return
            
        try:
            logger.info("🧠 Chargement du modèle Qwen...")
            
            # Utiliser un modèle Qwen plus petit pour de meilleures performances
            model_name = "Qwen/Qwen1.5-0.5B-Chat"
            
            # Vérifier la disponibilité GPU
            device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"🔧 Utilisation du device: {device}")
            
            # Charger le tokenizer
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                model_name, 
                trust_remote_code=True
            )
            
            # Charger le modèle avec optimisations
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                device_map="auto" if device == "cuda" else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            if device == "cpu":
                self.qwen_model = self.qwen_model.to(device)
            
            logger.info("✅ Modèle Qwen chargé avec succès")
            
        except Exception as e:
            logger.error(f"❌ Erreur chargement Qwen: {str(e)}")
            self.qwen_model = None
            self.qwen_tokenizer = None

    def enhance_image(self, image: Image.Image) -> Image.Image:
        """Amélioration de l'image pour un meilleur OCR"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage adaptatif
        denoised = cv2.fastNlMeansDenoising(img_array, None, 20, 7, 21)
        
        # Amélioration du contraste
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        return Image.fromarray(binary)

    def extract_text_and_position(self, pdf_path: str, pdf_name: str) -> Tuple[str, List[Dict]]:
        """Extrait le texte et les positions des mots avec OCR amélioré"""
        logger.info(f"📄 Extraction OCR avancée pour {pdf_name}")
        
        # Créer les dossiers
        os.makedirs(IMG_FOLDER, exist_ok=True)
        os.makedirs(HOCR_FOLDER, exist_ok=True)
        os.makedirs(TEXT_FOLDER, exist_ok=True)
        
        # Conversion PDF en images avec résolution élevée
        try:
            pages = convert_from_path(pdf_path, dpi=400, fmt='png')
        except Exception as e:
            logger.error(f"❌ Erreur conversion PDF: {str(e)}")
            return "", []
        
        all_text = ""
        all_words = []
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Traitement page {page_num}...")
            
            try:
                # Amélioration de l'image
                enhanced_page = self.enhance_image(page)
                
                # Sauvegarde de l'image
                img_path = f"{IMG_FOLDER}/{pdf_name}_page_{page_num}.png"
                enhanced_page.save(img_path, "PNG")
                
                # OCR en mode texte avec configuration standard (plus fiable)
                text = pytesseract.image_to_string(enhanced_page, config=self.ocr_config)
                all_text += f"\n--- Page {page_num} ---\n{text}"
                
                # OCR en mode HOCR pour les positions
                hocr_data = pytesseract.image_to_pdf_or_hocr(
                    enhanced_page, extension='hocr', config=self.ocr_config
                )
                
                # Sauvegarde HOCR
                hocr_path = f"{HOCR_FOLDER}/{pdf_name}_page_{page_num}.hocr"
                with open(hocr_path, 'wb') as f:
                    f.write(hocr_data)
                    
                # Analyse HOCR
                page_words = self.parse_hocr(hocr_data, page_num)
                all_words.extend(page_words)
                
                logger.info(f"✅ Page {page_num} traitée - {len(page_words)} mots détectés")
            except Exception as e:
                logger.error(f"❌ Erreur traitement page {page_num}: {str(e)}")
        
        # Sauvegarde du texte brut
        text_path = f"{TEXT_FOLDER}/{pdf_name}.txt"
        try:
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(all_text)
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde texte: {str(e)}")
        
        return all_text, all_words

    def parse_hocr(self, hocr_data: bytes, page_num: int) -> List[Dict]:
        """Analyse le format HOCR pour extraire les positions des mots"""
        words = []
        try:
            soup = BeautifulSoup(hocr_data, 'html.parser')
            
            # Recherche de tous les mots
            word_elements = soup.find_all(class_='ocrx_word')
            
            for word in word_elements:
                text = word.get_text().strip()
                if not text:
                    continue
                    
                # Extraction des coordonnées
                title = word.get('title', '')
                bbox = re.search(r'bbox (\d+) (\d+) (\d+) (\d+)', title)
                if not bbox:
                    continue
                    
                x1, y1, x2, y2 = map(int, bbox.groups())
                width = x2 - x1
                height = y2 - y1
                
                words.append({
                    "text": text,
                    "page": page_num,
                    "x": x1,
                    "y": y1,
                    "width": width,
                    "height": height
                })
        except Exception as e:
            logger.error(f"❌ Erreur analyse HOCR: {str(e)}")
        
        return words

    def call_qwen_analysis(self, text: str) -> Dict:
        """Analyse du contrat avec le modèle Qwen"""
        if not self.qwen_model or not self.qwen_tokenizer:
            logger.warning("⚠️ Modèle Qwen non disponible")
            return {}

        logger.info("🧠 Analyse avec Qwen...")

        try:
            # Prompt ultra-spécialisé pour l'extraction de contrats BTK
            prompt = f"""Tu es un expert juridique spécialisé dans l'analyse de contrats de la Banque Tuniso-Koweitienne (BTK).

MISSION : Extraire avec PRÉCISION MAXIMALE toutes les informations du contrat au format JSON.

STRUCTURE JSON ATTENDUE :
{{
  "informations_banque": {{
    "nom_banque": "",
    "capital_social": "",
    "numero_rc": "",
    "adresse": "",
    "forme_juridique": "",
    "telephone": ""
  }},
  "informations_contrat": {{
    "type_contrat": "",
    "numero_contrat": "",
    "date_edition": "",
    "montant_principal": "",
    "duree": "",
    "taux_interet": "",
    "commission_gestion": "",
    "garanties": ""
  }},
  "informations_client": {{
    "code_client": "",
    "nom_client": "",
    "adresse_client": "",
    "secteur_activite": "",
    "numero_rc_client": ""
  }}
}}

RÈGLES D'EXTRACTION STRICTES :
1. BANQUE : Cherche "BANQUE TUNISO-KOWEITIENNE", "B.T.K", capital en dinars, R.C., siège social
2. CONTRAT : Cherche "CONTRAT DE PRET", date "Edité le", montant "somme principale", durée en mois
3. CLIENT : Cherche "Code Client", nom après "Et", adresse client, R.C. client
4. DATES : Format JJ/MM/AAAA uniquement
5. MONTANTS : Inclure les chiffres même si partiels
6. MASQUÉS : Inclure xxx, XXXX comme valeurs exactes

TEXTE À ANALYSER :
{text[:10000]}

RÉPONSE JSON UNIQUEMENT :"""

            # Tokenisation
            inputs = self.qwen_tokenizer(
                prompt,
                return_tensors="pt",
                max_length=4096,
                truncation=True
            )

            # Génération avec le modèle
            with torch.no_grad():
                outputs = self.qwen_model.generate(
                    inputs.input_ids,
                    max_new_tokens=1500,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.qwen_tokenizer.eos_token_id,
                    eos_token_id=self.qwen_tokenizer.eos_token_id
                )

            # Décodage de la réponse
            response = self.qwen_tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )

            # Extraction du JSON de la réponse
            json_match = re.search(r'\{[\s\S]*\}', response)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    logger.info("✅ Analyse Qwen réussie")
                    return result
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Erreur JSON Qwen: {str(e)}")

        except Exception as e:
            logger.error(f"❌ Erreur analyse Qwen: {str(e)}")

        return {}

    def extract_with_regex_patterns(self, text: str) -> Dict:
        """Extraction avec patterns regex considérablement améliorés"""
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {}
        }

        # Nettoyage et préparation du texte
        clean_text = self.clean_and_prepare_text(text)

        # Patterns ultra-précis pour informations banque
        bank_patterns = {
            "nom_banque": [
                r"(?:La\s+)?BANQUE\s+TUNISO[\-\s]*KOWEITIENNE",
                r"B\.T\.K\.?",
                r"BTK\b"
            ],
            "capital_social": [
                r"capital\s+de\s+([\d\s,\.]+)\s*(?:de\s+)?dinars?",
                r"au\s+capital\s+de\s+([\d\s,\.]+)\s*dinars?",
                r"capital\s+social\s*:?\s*([\d\s,\.]+)"
            ],
            "numero_rc": [
                r"R\.?C\.?\s*[=:]\s*([A-Z]?\d+)",
                r"registre\s+(?:de\s+)?commerce\s*[=:]\s*([A-Z]?\d+)",
                r"immatriculée.*?sous.*?(?:n°|numéro)\s*([A-Z]?\d+)"
            ],
            "adresse": [
                r"siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+(?:,\s*[^,\n\.]+)*)",
                r"domiciliée?\s+(?:à\s+)?([^,\n\.]+)",
                r"(\d+\s*bis[,\s]+Avenue\s+[^,\n\.]+)"
            ],
            "forme_juridique": [
                r"(société\s+anonyme)",
                r"(S\.A\.?)",
                r"(SARL)"
            ],
            "telephone": [
                r"(?:Tél|Tel|Téléphone)\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})",
                r"(\+216\s*[\d\s\-]{8,})",
                r"(\d{2}\s*\d{3}\s*\d{3})"
            ]
        }

        # Patterns ultra-précis pour informations contrat
        contract_patterns = {
            "type_contrat": [
                r"CONTRAT\s+DE\s+(PRET|PRÊT)",
                r"contrat\s+de\s+(crédit|prêt)",
                r"(prêt|crédit)\s+bancaire"
            ],
            "numero_contrat": [
                r"Numéro\s+(?:de\s+)?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)",
                r"Référence\s*[=:]\s*([A-Z0-9\-\/]+)",
                r"N°\s*(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)"
            ],
            "date_edition": [
                r"Edité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                r"édité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                r"Date\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})"
            ],
            "montant_principal": [
                r"somme\s+principale\s+(?:de\s+)?([\d\s,\.]+)",
                r"montant\s+(?:du\s+)?(?:crédit|prêt)\s*[=:]\s*([\d\s,\.]+)",
                r"principal\s*[=:]\s*([\d\s,\.]+)"
            ],
            "duree": [
                r"durée\s+de\s+(\d+\s+mois)",
                r"pour\s+une\s+durée\s+de\s+(\d+\s+mois)",
                r"remboursable\s+en\s+(\d+\s+mois)"
            ],
            "taux_interet": [
                r"taux\s+semestriel\s+(?:du\s+marché\s+monétaire\s+)?(?:majoré\s+de\s+)?([\d,\.]+\s*%?)",
                r"intérêts\s+au\s+taux\s+(?:de\s+)?([\d,\.]+\s*%)",
                r"taux\s+d['\']intérêt\s*[=:]\s*([\d,\.]+\s*%)"
            ],
            "commission_gestion": [
                r"commission\s+de\s+gestion\s+de\s+([\d,\.]+\s*%)",
                r"commission\s*[=:]\s*([\d,\.]+\s*%)",
                r"frais\s+de\s+gestion\s*[=:]\s*([\d,\.]+\s*%)"
            ],
            "garanties": [
                r"garanties?\s*[=:]\s*([^\.]+)",
                r"hypothèque\s+sur\s+([^\.]+)",
                r"nantissement\s+(?:de\s+)?([^\.]+)"
            ]
        }

        # Patterns ultra-précis pour informations client
        client_patterns = {
            "code_client": [
                r"Code\s+Client\s*[=:]\s*([A-Z0-9X]{4,})",
                r"Identifiant\s+Client\s*[=:]\s*([A-Z0-9X]{4,})",
                r"PIN\s*[=:]\s*([A-Z0-9X]{4,})"
            ],
            "nom_client": [
                r"Et\s+([A-Z][A-Z\s&\-]+),?\s+dont\s+le\s+siège",
                r"Emprunteuse?\s*[=:]\s*([A-Z][A-Z\s&\-]+)",
                r"Client\s*[=:]\s*([A-Z][A-Z\s&\-]+)",
                r"Souscripteur\s*[=:]\s*([A-Z][A-Z\s&\-]+)"
            ],
            "adresse_client": [
                r"dont\s+le\s+siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)",
                r"domicilié(?:e)?\s+(?:à\s+)?([^,\n\.]+)",
                r"adresse\s*[=:]\s*([^,\n\.]+)"
            ],
            "numero_rc_client": [
                r"immatriculé(?:e)?\s+au\s+(?:Centre\s+National\s+du\s+)?Registre\s+(?:des\s+Entreprises\s+)?sous\s+(?:l['\']identifiant\s+unique\s+)?n°\s*([A-Z0-9]+)",
                r"R\.?C\.?\s+(?:client\s+)?[=:]\s*([A-Z0-9]+)"
            ],
            "secteur_activite": [
                r"Secteur\s+d['\']activité\s*[=:]\s*([^,\n\.]+)",
                r"activité\s*[=:]\s*([^,\n\.]+)"
            ]
        }

        # Application des patterns avec analyse contextuelle
        for section, patterns in [
            ("informations_banque", bank_patterns),
            ("informations_contrat", contract_patterns),
            ("informations_client", client_patterns)
        ]:
            section_results = self.apply_patterns_with_context(clean_text, patterns, section)
            result[section].update(section_results)

        return result

    def clean_and_prepare_text(self, text: str) -> str:
        """Nettoie et prépare le texte pour une meilleure extraction"""
        # Suppression des caractères de contrôle et normalisation des espaces
        clean_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', ' ', text)
        clean_text = re.sub(r'\s+', ' ', clean_text)

        # Correction des erreurs OCR communes
        corrections = {
            r'B\.T\.K\.': 'B.T.K.',
            r'BANQUE\s+TUNISO[\-\s]*KOWEITIENNE': 'BANQUE TUNISO-KOWEITIENNE',
            r'societe\s+anonyme': 'société anonyme',
            r'Edite\s+le': 'Edité le',
            r'duree\s+de': 'durée de',
            r'taux\s+d[\'\']interets?': 'taux d\'intérêt',
            r'commission\s+de\s+gestion': 'commission de gestion',
            r'R\.C\.': 'R.C.',
            r'siege\s+social': 'siège social'
        }

        for pattern, replacement in corrections.items():
            clean_text = re.sub(pattern, replacement, clean_text, flags=re.IGNORECASE)

        return clean_text

    def apply_patterns_with_context(self, text: str, patterns: Dict, section: str) -> Dict:
        """Applique les patterns avec analyse contextuelle"""
        results = {}

        # Diviser le texte en lignes pour une meilleure analyse contextuelle
        lines = text.split('\n')
        full_text = ' '.join(lines)

        for field, field_patterns in patterns.items():
            best_match = None
            best_score = 0

            for pattern in field_patterns:
                # Recherche dans le texte complet
                matches = list(re.finditer(pattern, full_text, re.IGNORECASE | re.MULTILINE))

                for match in matches:
                    value = match.group(1).strip() if match.groups() else match.group(0).strip()

                    # Validation et scoring du match
                    score = self.score_match(field, value, match.start(), full_text)

                    if score > best_score and self.validate_extracted_value(field, value):
                        best_match = value
                        best_score = score

            if best_match:
                results[field] = self.clean_extracted_value(field, best_match)

        return results

    def score_match(self, field: str, value: str, position: int, full_text: str) -> float:
        """Score un match basé sur le contexte et la qualité"""
        score = 1.0

        # Bonus pour la longueur appropriée
        length_bonus = {
            "nom_banque": (10, 50),
            "numero_rc": (3, 15),
            "date_edition": (8, 12),
            "montant_principal": (3, 20),
            "duree": (5, 15),
            "taux_interet": (3, 10),
            "nom_client": (5, 50),
            "code_client": (4, 20)
        }

        if field in length_bonus:
            min_len, max_len = length_bonus[field]
            if min_len <= len(value) <= max_len:
                score += 0.3
            elif len(value) < min_len or len(value) > max_len * 2:
                score -= 0.5

        # Bonus pour le contexte environnant
        context_start = max(0, position - 100)
        context_end = min(len(full_text), position + len(value) + 100)
        context = full_text[context_start:context_end].lower()

        context_keywords = {
            "nom_banque": ["banque", "btk", "tuniso"],
            "numero_rc": ["registre", "commerce", "immatriculée"],
            "date_edition": ["edité", "date"],
            "montant_principal": ["somme", "principal", "montant"],
            "duree": ["durée", "mois"],
            "taux_interet": ["taux", "intérêt"],
            "nom_client": ["client", "emprunteuse", "souscripteur"],
            "code_client": ["code", "identifiant"]
        }

        if field in context_keywords:
            keyword_count = sum(1 for keyword in context_keywords[field] if keyword in context)
            score += keyword_count * 0.2

        return max(0, score)

    def validate_extracted_value(self, field: str, value: str) -> bool:
        """Valide une valeur extraite selon des règles spécifiques"""
        if not value or len(value.strip()) < 2:
            return False

        # Validations spécifiques par champ
        validators = {
            "date_edition": lambda v: bool(re.match(r'\d{1,2}\/\d{1,2}\/\d{4}', v)),
            "numero_rc": lambda v: bool(re.match(r'[A-Z]?\d+', v)) and len(v) >= 3,
            "taux_interet": lambda v: bool(re.search(r'\d+', v)),
            "duree": lambda v: bool(re.search(r'\d+', v)),
            "montant_principal": lambda v: bool(re.search(r'\d+', v)),
            "code_client": lambda v: len(v) >= 4 and bool(re.match(r'[A-Z0-9X]+', v)),
            "telephone": lambda v: bool(re.search(r'\d{8,}', v.replace(' ', '').replace('-', '')))
        }

        if field in validators:
            return validators[field](value)

        # Validation générale : pas que des caractères spéciaux
        if re.match(r'^[^a-zA-Z0-9À-ÿ]+$', value):
            return False

        return True

    def clean_extracted_value(self, field: str, value: str) -> str:
        """Nettoie une valeur extraite"""
        # Nettoyage de base
        clean_value = value.strip()
        clean_value = re.sub(r'\s+', ' ', clean_value)

        # Nettoyages spécifiques par champ
        if field == "nom_banque":
            clean_value = re.sub(r'^(?:La\s+)?', '', clean_value, flags=re.IGNORECASE)
        elif field == "adresse" or field == "adresse_client":
            clean_value = re.sub(r'^(?:à\s+)?', '', clean_value, flags=re.IGNORECASE)
        elif field == "forme_juridique":
            clean_value = clean_value.lower()
        elif field in ["taux_interet", "commission_gestion"]:
            if not clean_value.endswith('%') and re.search(r'\d', clean_value):
                clean_value += '%'

        return clean_value

    def detect_masked_fields_enhanced(self, words: List[Dict]) -> List[Dict]:
        """Détection améliorée des champs masqués avec analyse contextuelle"""
        masked_fields = []
        if not words:
            return masked_fields

        try:
            # Tri des mots par position (page, y, x)
            sorted_words = sorted(words, key=lambda w: (w['page'], w['y'], w['x']))

            # Groupement par ligne avec tolérance améliorée
            lines = []
            current_line = []
            current_y = sorted_words[0]['y'] if sorted_words else 0

            for word in sorted_words:
                # Tolérance verticale adaptative
                tolerance = max(word['height'] * 0.4, 15)

                if abs(word['y'] - current_y) <= tolerance:
                    current_line.append(word)
                else:
                    if current_line:
                        current_line.sort(key=lambda w: w['x'])
                        lines.append(current_line)
                    current_line = [word]
                    current_y = word['y']

            if current_line:
                current_line.sort(key=lambda w: w['x'])
                lines.append(current_line)

            # Recherche des labels et champs masqués avec contexte étendu
            for i, line in enumerate(lines):
                line_text = " ".join(w["text"] for w in line)

                # Vérification des labels avec patterns flexibles
                for field, labels in self.field_mapping.items():
                    for label in labels:
                        # Recherche du label avec flexibilité
                        pattern = rf'\b{re.escape(label)}\b'
                        if re.search(pattern, line_text, re.IGNORECASE):

                            # Recherche dans la même ligne
                            masked_in_line = self.find_masked_in_line(line, labels, field)
                            masked_fields.extend(masked_in_line)

                            # Recherche dans les lignes suivantes (jusqu'à 3 lignes)
                            for j in range(1, min(4, len(lines) - i)):
                                next_line = lines[i + j]
                                masked_in_next = self.find_masked_in_line(next_line, labels, field)
                                masked_fields.extend(masked_in_next)

                                # Arrêter si on trouve un autre label
                                next_line_text = " ".join(w["text"] for w in next_line)
                                if any(re.search(rf'\b{re.escape(l)}\b', next_line_text, re.IGNORECASE)
                                      for other_labels in self.field_mapping.values()
                                      for l in other_labels if l != label):
                                    break

        except Exception as e:
            logger.error(f"❌ Erreur détection champs masqués: {str(e)}")

        return masked_fields

    def find_masked_in_line(self, line: List[Dict], labels: List[str], field: str) -> List[Dict]:
        """Trouve les champs masqués dans une ligne"""
        masked_fields = []

        for word in line:
            if self.is_masked_enhanced(word["text"]):
                # Vérifier que ce n'est pas un label
                if not any(l.lower() in word["text"].lower() for l in labels):
                    masked_fields.append({
                        "champ": field,
                        "masque": word["text"],
                        "position": {
                            "page": word["page"],
                            "x": word["x"],
                            "y": word["y"],
                            "width": word["width"],
                            "height": word["height"]
                        },
                        "confiance": self.calculate_mask_confidence(word["text"])
                    })

        return masked_fields

    def is_masked_enhanced(self, text: str) -> bool:
        """Vérifie si le texte est un champ masqué avec patterns étendus"""
        if len(text) < 2:
            return False

        # Patterns de masquage étendus
        masked_patterns = [
            r'^X{2,}$',           # Séquence de X (2 ou plus)
            r'^\*{2,}$',          # Séquence d'astérisques
            r'^•{2,}$',           # Séquence de points
            r'^_{3,}$',           # Séquence de underscores
            r'^#{2,}$',           # Séquence de dièses
            r'^[X*•_#\s]{3,}$',   # Combinaison de caractères de masquage
            r'^xxx+$',            # Variations de xxx
            r'^XXX+$',            # Variations de XXX
            r'^\[.*\]$',          # Texte entre crochets
            r'^<.*>$',            # Texte entre chevrons
            r'^\{.*\}$'           # Texte entre accolades
        ]

        for pattern in masked_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True

        # Vérification de la densité de caractères de masquage
        mask_chars = sum(1 for c in text if c in 'X*•_#')
        if len(text) > 0 and mask_chars / len(text) > 0.7:
            return True

        return False

    def calculate_mask_confidence(self, text: str) -> float:
        """Calcule la confiance que le texte est masqué"""
        if not text:
            return 0.0

        # Facteurs de confiance
        mask_chars = sum(1 for c in text if c in 'X*•_#')
        length_factor = min(len(text) / 10, 1.0)  # Plus long = plus confiant
        density_factor = mask_chars / len(text) if len(text) > 0 else 0

        # Bonus pour patterns typiques
        pattern_bonus = 0.2 if re.match(r'^[X*•_#]{3,}$', text) else 0

        confidence = (density_factor * 0.6 + length_factor * 0.2 + pattern_bonus)
        return min(confidence, 1.0)

    def merge_extraction_results(self, regex_result: Dict, qwen_result: Dict) -> Dict:
        """Fusionne les résultats d'extraction regex et Qwen"""
        merged = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {}
        }

        for section in merged.keys():
            # Commencer avec les résultats regex
            merged[section] = regex_result.get(section, {}).copy()

            # Ajouter/remplacer avec les résultats Qwen (priorité à Qwen)
            qwen_section = qwen_result.get(section, {})
            for field, value in qwen_section.items():
                if value and value.strip():  # Seulement si Qwen a trouvé quelque chose
                    merged[section][field] = value

        return merged

    def validate_and_clean_results(self, result: Dict) -> Dict:
        """Valide et nettoie les résultats extraits"""
        cleaned = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {}
        }

        for section, fields in result.items():
            if section in cleaned:
                for field, value in fields.items():
                    if value and isinstance(value, str):
                        # Nettoyage de base
                        clean_value = value.strip()
                        clean_value = re.sub(r'\s+', ' ', clean_value)

                        # Validation spécifique par type de champ
                        if self.validate_field_value(field, clean_value):
                            cleaned[section][field] = clean_value

        return cleaned

    def validate_field_value(self, field: str, value: str) -> bool:
        """Valide la valeur d'un champ spécifique"""
        if not value or len(value.strip()) < 2:
            return False

        # Validations spécifiques
        validations = {
            "date_edition": lambda v: bool(re.match(r'\d{1,2}/\d{1,2}/\d{4}', v)),
            "numero_rc": lambda v: bool(re.match(r'[A-Z]?\d+', v)),
            "taux_interet": lambda v: bool(re.search(r'\d+', v)),
            "duree": lambda v: bool(re.search(r'\d+', v)),
            "montant_principal": lambda v: bool(re.search(r'\d+', v))
        }

        if field in validations:
            return validations[field](value)

        return True  # Par défaut, accepter la valeur

    def process_contract_enhanced(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement complet d'un contrat avec OCR + Qwen"""
        logger.info(f"🚀 TRAITEMENT AVANCÉ: {pdf_name}")

        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "champs_masques": [],
            "scores_qualite": {}
        }

        try:
            # 1. Extraction OCR avec positions
            logger.info("📄 Phase 1: Extraction OCR haute résolution...")
            ocr_text, word_positions = self.extract_text_and_position(pdf_path, pdf_name)

            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")

            logger.info(f"📝 Longueur du texte: {len(ocr_text)} caractères")

            # 2. Extraction avec patterns regex améliorés
            logger.info("🔍 Phase 2: Extraction avec patterns regex...")
            regex_results = self.extract_with_regex_patterns(ocr_text)

            # 3. Analyse avec Qwen (si disponible)
            qwen_results = {}
            if self.qwen_model:
                logger.info("🧠 Phase 3: Analyse avec Qwen...")
                qwen_results = self.call_qwen_analysis(ocr_text)
            else:
                logger.info("⚠️ Phase 3: Qwen non disponible - utilisation regex seul")

            # 4. Fusion des résultats
            logger.info("🔄 Phase 4: Fusion des résultats...")
            merged_results = self.merge_extraction_results(regex_results, qwen_results)

            # 5. Validation et nettoyage
            logger.info("✨ Phase 5: Validation et nettoyage...")
            final_results = self.validate_and_clean_results(merged_results)

            # 6. Détection des champs masqués
            logger.info("🎭 Phase 6: Détection des champs masqués...")
            masked_fields = self.detect_masked_fields_enhanced(word_positions)

            # 7. Calcul des scores de qualité
            quality_scores = self.calculate_quality_scores(final_results, masked_fields)

            # 8. Assemblage du résultat final
            result.update(final_results)
            result["champs_masques"] = masked_fields
            result["scores_qualite"] = quality_scores

            # 9. Ajout des métadonnées
            result['metadata'] = {
                'fichier_source': f"{pdf_name}.pdf",
                'extraction_timestamp': datetime.now().isoformat(),
                'methode_extraction': 'Qwen + OCR Enhanced Extractor',
                'ocr_text_length': len(ocr_text),
                'masked_fields_count': len(masked_fields),
                'qwen_available': self.qwen_model is not None,
                'total_words_detected': len(word_positions),
                'quality_score_global': quality_scores.get('score_global', 0)
            }

            # 10. Affichage du résumé
            self.display_extraction_summary(result)

            logger.info(f"✅ TRAITEMENT TERMINÉ avec succès!")
            return result

        except Exception as e:
            logger.error(f"❌ Erreur lors du traitement de {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf",
                "informations_banque": {},
                "informations_contrat": {},
                "informations_client": {},
                "champs_masques": [],
                "scores_qualite": {"score_global": 0}
            }

    def calculate_quality_scores(self, results: Dict, masked_fields: List[Dict]) -> Dict:
        """Calcule les scores de qualité de l'extraction"""
        scores = {
            "score_global": 0,
            "completude_banque": 0,
            "completude_contrat": 0,
            "completude_client": 0,
            "champs_masques_detectes": len(masked_fields)
        }

        # Calcul de la complétude par section
        for section, required in self.required_fields.items():
            section_key = f"completude_{section.split('_')[1]}"
            found_fields = len([f for f in required if results.get(section, {}).get(f)])
            scores[section_key] = (found_fields / len(required)) * 100

        # Score global (moyenne pondérée)
        scores["score_global"] = (
            scores["completude_banque"] * 0.2 +
            scores["completude_contrat"] * 0.5 +
            scores["completude_client"] * 0.3
        )

        return scores

    def display_extraction_summary(self, result: Dict):
        """Affiche un résumé de l'extraction"""
        logger.info("📊 === RÉSUMÉ DE L'EXTRACTION ===")

        # Informations trouvées
        for section in ["informations_banque", "informations_contrat", "informations_client"]:
            section_name = section.replace("informations_", "").upper()
            found = len([v for v in result.get(section, {}).values() if v])
            total = len(self.required_fields.get(section, []))
            logger.info(f"📋 {section_name}: {found}/{total} champs trouvés")

        # Champs masqués
        masked_count = len(result.get("champs_masques", []))
        logger.info(f"🎭 Champs masqués détectés: {masked_count}")

        # Score de qualité
        quality = result.get("scores_qualite", {}).get("score_global", 0)
        logger.info(f"⭐ Score de qualité global: {quality:.1f}%")

def main():
    """Fonction principale"""
    logger.info("🎯 === EXTRACTEUR DE CONTRATS BTK AVEC QWEN ===")

    # Initialisation
    extractor = QwenBTKContractExtractor()

    # Vérification des dossiers
    if not os.path.exists(PDF_FOLDER):
        logger.error(f"❌ Dossier {PDF_FOLDER} non trouvé")
        return

    # Création des dossiers de sortie
    for folder in [IMG_FOLDER, HOCR_FOLDER, RESULT_FOLDER, TEXT_FOLDER]:
        os.makedirs(folder, exist_ok=True)

    # Recherche des fichiers PDF
    pdf_files = [f for f in os.listdir(PDF_FOLDER)
                 if f.lower().endswith('.pdf') and not f.startswith('~')]

    if not pdf_files:
        logger.error(f"❌ Aucun fichier PDF trouvé dans {PDF_FOLDER}")
        return

    logger.info(f"📁 Trouvé {len(pdf_files)} fichier(s) PDF à traiter")

    # Statistiques globales
    total_quality = 0
    successful_extractions = 0

    # Traitement de chaque fichier
    for i, pdf_file in enumerate(pdf_files, 1):
        logger.info(f"\n{'='*80}")
        logger.info(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        logger.info(f"{'='*80}")

        pdf_path = os.path.join(PDF_FOLDER, pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]

        # Traitement
        result = extractor.process_contract_enhanced(pdf_path, pdf_name)

        # Sauvegarde
        output_path = f"{RESULT_FOLDER}/{pdf_name}_QWEN_BTK.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Résultat sauvegardé: {output_path}")

        # Mise à jour des statistiques
        if "erreur" not in result:
            successful_extractions += 1
            total_quality += result.get("scores_qualite", {}).get("score_global", 0)

    # Résumé final
    logger.info(f"\n{'='*80}")
    logger.info("🏁 EXTRACTION TERMINÉE!")
    logger.info(f"✅ Extractions réussies: {successful_extractions}/{len(pdf_files)}")
    if successful_extractions > 0:
        avg_quality = total_quality / successful_extractions
        logger.info(f"⭐ Qualité moyenne: {avg_quality:.1f}%")
    logger.info(f"{'='*80}")

if __name__ == "__main__":
    main()
