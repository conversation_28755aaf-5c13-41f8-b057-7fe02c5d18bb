#!/usr/bin/env python3
"""
Test du système avec Qwen 2.5 pour améliorer les résultats
"""

import os
import json
import time
from detection_qwen_enhanced import QwenBTKContractExtractor

def test_qwen25_performance():
    """Test de performance avec Qwen 2.5"""
    
    print("🚀 === TEST QWEN 2.5 POUR CONTRATS BTK ===")
    print("="*60)
    
    # Initialisation
    start_time = time.time()
    extractor = QwenBTKContractExtractor()
    init_time = time.time() - start_time
    
    print(f"⏱️  Temps d'initialisation: {init_time:.2f}s")
    
    # Fichier de test
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"\n🧪 Test Qwen 2.5 sur : {test_file}")
    print("="*60)
    
    # Traitement avec mesure du temps
    start_process = time.time()
    result = extractor.process_contract_enhanced(pdf_path, pdf_name)
    process_time = time.time() - start_process
    
    print(f"\n⏱️  Temps de traitement total: {process_time:.2f}s")
    
    # Analyse détaillée des résultats
    print("\n📊 ANALYSE DÉTAILLÉE DES RÉSULTATS QWEN 2.5:")
    print("="*60)
    
    # Statistiques par section
    sections_stats = {}
    for section in ["informations_banque", "informations_contrat", "informations_client", "informations_signatures"]:
        section_name = section.replace("informations_", "").upper()
        section_data = result.get(section, {})
        
        # Compter les champs remplis
        filled_fields = sum(1 for v in section_data.values() if v and str(v).strip())
        total_fields = len(extractor.required_fields.get(section, []))
        
        sections_stats[section] = {
            "filled": filled_fields,
            "total": total_fields,
            "percentage": (filled_fields / total_fields * 100) if total_fields > 0 else 0
        }
        
        print(f"\n🏷️  {section_name}:")
        print(f"   📈 Complétude: {filled_fields}/{total_fields} ({sections_stats[section]['percentage']:.1f}%)")
        
        if section_data:
            for field, value in section_data.items():
                status = "✅" if value and str(value).strip() else "❌"
                display_value = str(value)[:80] + "..." if len(str(value)) > 80 else str(value)
                print(f"   {status} {field}: {display_value}")
        else:
            print("   ❌ Aucune information trouvée")
    
    # Analyse des champs masqués
    masked = result.get("champs_masques", [])
    print(f"\n🎭 CHAMPS MASQUÉS DÉTECTÉS ({len(masked)}):")
    
    # Grouper par type de masque
    mask_types = {}
    for field in masked:
        mask = field['masque']
        if mask not in mask_types:
            mask_types[mask] = []
        mask_types[mask].append(field['champ'])
    
    for mask, fields in mask_types.items():
        print(f"   🔒 '{mask}': {len(fields)} occurrences ({', '.join(set(fields))})")
    
    # Scores de qualité
    scores = result.get("scores_qualite", {})
    print(f"\n⭐ SCORES DE QUALITÉ QWEN 2.5:")
    print(f"   🎯 Score global: {scores.get('score_global', 0):.1f}%")
    print(f"   🏦 Banque: {scores.get('completude_banque', 0):.1f}%")
    print(f"   📄 Contrat: {scores.get('completude_contrat', 0):.1f}%")
    print(f"   👤 Client: {scores.get('completude_client', 0):.1f}%")
    print(f"   ✍️  Signatures: {scores.get('completude_signatures', 0):.1f}%")
    print(f"   🔥 Champs critiques: {scores.get('champs_critiques_detectes', 0):.1f}%")
    
    # Comparaison avec version précédente
    print(f"\n📈 AMÉLIORATIONS QWEN 2.5:")
    improvements = {
        "Détection globale": f"{scores.get('score_global', 0):.1f}% (vs ~37% précédent)",
        "Champs critiques": f"{scores.get('champs_critiques_detectes', 0):.1f}% (vs ~62% précédent)",
        "Champs masqués": f"{len(masked)} détectés (vs ~67 précédent)",
        "Temps traitement": f"{process_time:.1f}s"
    }
    
    for metric, value in improvements.items():
        print(f"   📊 {metric}: {value}")
    
    # Sauvegarde avec timestamp
    timestamp = int(time.time())
    output_path = f"results/{pdf_name}_QWEN25_{timestamp}.json"
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Résultat Qwen 2.5 sauvegardé: {output_path}")
    
    # Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    if scores.get('score_global', 0) > 50:
        print("   ✅ Excellente performance Qwen 2.5 - Système prêt pour production")
    elif scores.get('score_global', 0) > 35:
        print("   ⚠️  Bonne performance - Quelques ajustements recommandés")
    else:
        print("   🔧 Performance à améliorer - Vérifier les patterns et prompts")
    
    if len(masked) > 50:
        print("   🎭 Excellente détection des champs masqués")
    
    if scores.get('champs_critiques_detectes', 0) > 70:
        print("   🔥 Champs critiques bien détectés")
    
    return result

def compare_models():
    """Compare les performances entre différentes versions"""
    print(f"\n🔄 COMPARAISON DES MODÈLES:")
    print("="*60)
    
    # Ici on pourrait comparer avec les résultats précédents
    # En lisant les fichiers JSON existants
    
    previous_files = [
        "results/CDTGAR1_000307_250630_141635_QWEN_BTK.json",
        "results/CDTGAR1_000307_250630_141635_TEST_IMPROVED.json"
    ]
    
    for file_path in previous_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    score = data.get('scores_qualite', {}).get('score_global', 0)
                    method = data.get('metadata', {}).get('methode_extraction', 'Unknown')
                    print(f"   📊 {method}: {score:.1f}%")
            except Exception as e:
                print(f"   ❌ Erreur lecture {file_path}: {e}")

if __name__ == "__main__":
    try:
        result = test_qwen25_performance()
        compare_models()
        
        print(f"\n🎉 TEST QWEN 2.5 TERMINÉ AVEC SUCCÈS!")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ ERREUR DURANT LE TEST: {str(e)}")
        import traceback
        traceback.print_exc()
