#!/usr/bin/env python3
"""
Test du système hybride Patterns Regex + Qwen 2.5
"""

import os
import json
import time
from detection_hybrid_qwen import HybridQwenExtractor

def test_hybrid_system():
    """Test complet du système hybride"""
    
    print("🚀 === TEST SYSTÈME HYBRIDE REGEX + QWEN 2.5 ===")
    print("="*70)
    
    # Initialisation avec mesure du temps
    start_init = time.time()
    extractor = HybridQwenExtractor()
    init_time = time.time() - start_init
    
    print(f"⏱️  Initialisation: {init_time:.2f}s")
    
    # Vérifier les composants
    qwen_status = "✅ Chargé" if extractor.qwen_model else "❌ Non disponible"
    print(f"🧠 Qwen 2.5-1.5B: {qwen_status}")
    print(f"🔍 Patterns regex: ✅ Configurés")
    print(f"📋 Champs possibles: {sum(len(fields) for fields in extractor.all_possible_fields.values())}")
    
    # Test sur fichier
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"\n🧪 Test hybride sur: {test_file}")
    print("="*70)
    
    # Traitement hybride avec mesure détaillée
    start_process = time.time()
    result = extractor.process_contract_hybrid(pdf_path, pdf_name)
    process_time = time.time() - start_process
    
    print(f"\n⏱️  Temps de traitement total: {process_time:.2f}s")
    
    # Analyse détaillée des résultats
    if "erreur" not in result:
        print(f"\n📊 ANALYSE DÉTAILLÉE DU SYSTÈME HYBRIDE:")
        print("="*70)
        
        # Statistiques globales
        scores = result.get("scores_qualite", {})
        details = result.get("extraction_details", {})
        metadata = result.get("metadata", {})
        
        print(f"🎯 PERFORMANCES GLOBALES:")
        print(f"   • Score global: {scores.get('score_global', 0):.1f}%")
        print(f"   • Champs détectés: {scores.get('champs_detectes', 0)}")
        print(f"   • Champs obligatoires: {scores.get('champs_obligatoires_detectes', 0):.1f}%")
        print(f"   • Système hybride: {metadata.get('hybrid_system', False)}")
        
        # Contribution détaillée de chaque méthode
        print(f"\n🔧 CONTRIBUTION DES MÉTHODES:")
        regex_found = details.get('regex_fields_found', 0)
        qwen_found = details.get('qwen_fields_found', 0)
        hybrid_final = details.get('hybrid_fields_final', 0)
        improvement = details.get('improvement_over_regex', 0)
        
        print(f"   🔍 Patterns Regex: {regex_found} champs")
        print(f"   🧠 Qwen 2.5: {qwen_found} champs")
        print(f"   🔄 Fusion finale: {hybrid_final} champs")
        print(f"   📈 Amélioration: +{improvement} champs vs regex seul")
        
        if qwen_found > 0:
            qwen_contribution = (qwen_found / hybrid_final * 100) if hybrid_final > 0 else 0
            print(f"   🧠 Contribution Qwen: {qwen_contribution:.1f}%")
        
        # Analyse par section avec détails
        sections_info = {
            "informations_banque": "🏦 BANQUE",
            "informations_contrat": "📄 CONTRAT", 
            "informations_client": "👤 CLIENT",
            "informations_signatures": "✍️  SIGNATURES"
        }
        
        total_fields_found = 0
        total_fields_possible = 0
        
        for section, section_name in sections_info.items():
            section_data = result.get(section, {})
            section_score = scores.get(f"completude_{section.split('_')[1]}", 0)
            
            # Compter les champs
            fields_found = sum(1 for v in section_data.values() if v and str(v).strip())
            fields_possible = len(extractor.all_possible_fields.get(section, {}))
            
            total_fields_found += fields_found
            total_fields_possible += fields_possible
            
            print(f"\n{section_name}: {fields_found}/{fields_possible} ({section_score:.1f}%)")
            
            # Afficher les champs trouvés avec source
            if section_data:
                for field, value in section_data.items():
                    if value and str(value).strip():
                        # Identifier la source probable
                        if str(value).startswith("[MASQUÉ:"):
                            source = "🎭"
                        elif len(str(value)) > 20 and not re.match(r'^[A-Z0-9\-\/]+$', str(value)):
                            source = "🧠"  # Probablement Qwen
                        else:
                            source = "🔍"  # Probablement Regex
                        
                        display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"   {source} {field}: {display_value}")
            
            # Afficher quelques champs manqués
            missing_fields = []
            for field in extractor.all_possible_fields.get(section, {}):
                if not section_data.get(field):
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"   ❌ Manqués: {', '.join(missing_fields[:3])}" + 
                      (f" (+{len(missing_fields)-3})" if len(missing_fields) > 3 else ""))
        
        # Comparaison avec toutes les versions précédentes
        print(f"\n📈 COMPARAISON AVEC TOUTES LES VERSIONS:")
        comparisons = {
            "Version originale": {"score": 22, "champs": 6, "temps": "~60s", "methode": "Patterns basiques"},
            "Version améliorée": {"score": 37, "champs": 8, "temps": "~90s", "methode": "Patterns + validation"},
            "Version simple": {"score": 27, "champs": 6, "temps": "~12s", "methode": "Patterns optimisés"},
            "Système complet": {"score": 26, "champs": 19, "temps": "~48s", "methode": "Patterns ultra + masqués"},
            "Système hybride": {
                "score": scores.get('score_global', 0), 
                "champs": scores.get('champs_detectes', 0),
                "temps": f"{process_time:.1f}s",
                "methode": "Regex + Qwen 2.5"
            }
        }
        
        for version, data in comparisons.items():
            if version == "Système hybride":
                status = "🎉" if data["score"] > 35 else "📊"
                print(f"   {status} {version}: {data['score']:.1f}% ({data['champs']} champs) en {data['temps']} - {data['methode']}")
            else:
                print(f"   📊 {version}: {data['score']}% ({data['champs']} champs) en {data['temps']} - {data['methode']}")
        
        # Analyse de l'efficacité hybride
        print(f"\n🔬 ANALYSE DE L'EFFICACITÉ HYBRIDE:")
        hybrid_score = scores.get('score_global', 0)
        
        if hybrid_score > 40:
            print("   🎉 EXCELLENT: Le système hybride dépasse 40% - Prêt pour production!")
        elif hybrid_score > 30:
            print("   ✅ TRÈS BON: Le système hybride montre de bonnes performances")
        elif hybrid_score > 25:
            print("   ⚠️  BON: Le système hybride améliore les résultats")
        else:
            print("   🔧 À AMÉLIORER: Le système hybride nécessite des ajustements")
        
        # Recommandations spécifiques
        print(f"\n💡 RECOMMANDATIONS HYBRIDES:")
        
        if improvement > 5:
            print("   ✅ Qwen apporte une amélioration significative")
        elif improvement > 0:
            print("   ⚠️  Qwen apporte une amélioration modeste")
        else:
            print("   🔧 Qwen n'améliore pas les résultats - Vérifier prompts")
        
        if scores.get('champs_obligatoires_detectes', 0) > 70:
            print("   🔥 Excellente détection des champs obligatoires")
        else:
            print("   ⚠️  Améliorer la détection des champs obligatoires")
        
        if process_time < 60:
            print("   ⚡ Temps de traitement acceptable")
        else:
            print("   ⏱️  Temps de traitement élevé - Optimiser Qwen")
        
        # Sauvegarde avec timestamp
        timestamp = int(time.time())
        output_path = f"results/{pdf_name}_HYBRID_QWEN_{timestamp}.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Résultat hybride sauvegardé: {output_path}")
        
        return result, hybrid_score, process_time
    
    else:
        print(f"❌ Erreur: {result.get('erreur')}")
        return None, 0, process_time

def analyze_hybrid_advantages():
    """Analyse des avantages du système hybride"""
    
    print(f"\n🔍 AVANTAGES DU SYSTÈME HYBRIDE:")
    print("="*70)
    
    advantages = {
        "🔍 Patterns Regex": [
            "Précision maximale pour patterns connus",
            "Vitesse d'exécution rapide",
            "Fiabilité sur formats standardisés",
            "Pas de dépendance modèle"
        ],
        "🧠 Qwen 2.5": [
            "Intelligence contextuelle avancée",
            "Compréhension sémantique",
            "Adaptation aux variations",
            "Extraction de champs complexes"
        ],
        "🔄 Fusion Hybride": [
            "Combine le meilleur des deux approches",
            "Validation croisée des résultats",
            "Robustesse accrue",
            "Performance optimisée"
        ]
    }
    
    for category, benefits in advantages.items():
        print(f"\n{category}:")
        for benefit in benefits:
            print(f"   ✅ {benefit}")

if __name__ == "__main__":
    try:
        result, score, time_taken = test_hybrid_system()
        analyze_hybrid_advantages()
        
        print(f"\n🎉 TEST SYSTÈME HYBRIDE TERMINÉ!")
        if result:
            print(f"📊 Score final hybride: {score:.1f}%")
            print(f"⏱️  Temps total: {time_taken:.1f}s")
        print("="*70)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()
