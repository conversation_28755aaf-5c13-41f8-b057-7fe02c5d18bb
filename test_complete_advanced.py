#!/usr/bin/env python3
"""
Test du système complet avancé - Détection de TOUS les champs
"""

import os
import json
import time
from detection_complete_advanced import CompleteAdvancedExtractor

def test_complete_advanced_system():
    """Test du système complet avancé"""
    
    print("🚀 === TEST SYSTÈME COMPLET AVANCÉ ===")
    print("="*60)
    
    # Initialisation
    start_time = time.time()
    extractor = CompleteAdvancedExtractor()
    init_time = time.time() - start_time
    
    print(f"⏱️  Initialisation: {init_time:.2f}s")
    print(f"📋 Champs possibles configurés: {sum(len(fields) for fields in extractor.all_possible_fields.values())}")
    
    # Test sur fichier
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"\n🧪 Test complet sur: {test_file}")
    print("="*60)
    
    # Traitement complet
    start_process = time.time()
    result = extractor.process_contract_complete(pdf_path, pdf_name)
    process_time = time.time() - start_process
    
    print(f"\n⏱️  Temps de traitement: {process_time:.2f}s")
    
    # Analyse détaillée des résultats
    if "erreur" not in result:
        print(f"\n📊 ANALYSE COMPLÈTE DES RÉSULTATS:")
        print("="*60)
        
        # Statistiques globales
        scores = result.get("scores_qualite", {})
        metadata = result.get("metadata", {})
        
        print(f"🎯 SCORES GLOBAUX:")
        print(f"   • Score global: {scores.get('score_global', 0):.1f}%")
        print(f"   • Champs détectés: {scores.get('champs_detectes', 0)}")
        print(f"   • Champs masqués: {scores.get('champs_masques', 0)}")
        print(f"   • Champs obligatoires: {scores.get('champs_obligatoires_detectes', 0):.1f}%")
        
        # Analyse par section
        sections_info = {
            "informations_banque": "🏦 BANQUE",
            "informations_contrat": "📄 CONTRAT", 
            "informations_client": "👤 CLIENT",
            "informations_signatures": "✍️  SIGNATURES",
            "informations_financieres": "💰 FINANCIÈRES"
        }
        
        total_fields_found = 0
        total_fields_possible = 0
        
        for section, section_name in sections_info.items():
            section_data = result.get(section, {})
            section_score = scores.get(f"completude_{section.split('_')[1]}", 0)
            
            # Compter les champs
            fields_found = sum(1 for v in section_data.values() if v and str(v).strip())
            fields_possible = len(extractor.all_possible_fields.get(section, {}))
            
            total_fields_found += fields_found
            total_fields_possible += fields_possible
            
            print(f"\n{section_name}: {fields_found}/{fields_possible} ({section_score:.1f}%)")
            
            # Afficher les champs trouvés
            if section_data:
                for field, value in section_data.items():
                    if value and str(value).strip():
                        # Distinguer les valeurs masquées
                        if str(value).startswith("[MASQUÉ:"):
                            status = "🎭"
                            display_value = value
                        else:
                            status = "✅"
                            display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        
                        print(f"   {status} {field}: {display_value}")
            
            # Afficher quelques champs manqués
            missing_fields = []
            for field in extractor.all_possible_fields.get(section, {}):
                if not section_data.get(field):
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"   ❌ Manqués: {', '.join(missing_fields[:3])}" + 
                      (f" (+{len(missing_fields)-3} autres)" if len(missing_fields) > 3 else ""))
        
        # Analyse des champs masqués
        masked_fields = result.get("champs_masques", [])
        if masked_fields:
            print(f"\n🎭 CHAMPS MASQUÉS DÉTECTÉS ({len(masked_fields)}):")
            
            # Grouper par type de masque
            mask_types = {}
            for field in masked_fields:
                mask = field.get('valeur_masquee', 'Unknown')
                if mask not in mask_types:
                    mask_types[mask] = []
                mask_types[mask].append({
                    'champ': field.get('champ', 'Unknown'),
                    'confiance': field.get('confiance', 0),
                    'obligatoire': field.get('obligatoire', False)
                })
            
            for mask, fields in mask_types.items():
                avg_confidence = sum(f['confiance'] for f in fields) / len(fields)
                required_count = sum(1 for f in fields if f['obligatoire'])
                
                print(f"   🔒 '{mask}': {len(fields)} occurrences (conf: {avg_confidence:.2f}, {required_count} obligatoires)")
                
                # Afficher les champs les plus importants
                important_fields = sorted(fields, key=lambda x: (x['obligatoire'], x['confiance']), reverse=True)[:3]
                for field in important_fields:
                    req_marker = "🔥" if field['obligatoire'] else "📋"
                    print(f"     {req_marker} {field['champ']} (conf: {field['confiance']:.2f})")
        
        # Labels détectés
        labels_detected = result.get("labels_detectes", {})
        if labels_detected:
            print(f"\n🏷️  LABELS DÉTECTÉS ({len(labels_detected)}):")
            for field, info in labels_detected.items():
                print(f"   📌 {field}: {info.get('label_detecte', 'N/A')}")
        
        # Comparaison avec versions précédentes
        print(f"\n📈 COMPARAISON AVEC VERSIONS PRÉCÉDENTES:")
        comparisons = {
            "Version originale": {"score": 22, "champs": 6, "temps": "~60s"},
            "Version améliorée": {"score": 37, "champs": 8, "temps": "~90s"},
            "Version simple": {"score": 27, "champs": 6, "temps": "~12s"},
            "Système complet": {
                "score": scores.get('score_global', 0), 
                "champs": scores.get('champs_detectes', 0),
                "temps": f"{process_time:.1f}s"
            }
        }
        
        for version, data in comparisons.items():
            if version == "Système complet":
                status = "🎉" if data["score"] > 40 else "📊"
                print(f"   {status} {version}: {data['score']:.1f}% ({data['champs']} champs) en {data['temps']}")
            else:
                print(f"   📊 {version}: {data['score']}% ({data['champs']} champs) en {data['temps']}")
        
        # Recommandations
        print(f"\n💡 RECOMMANDATIONS:")
        global_score = scores.get('score_global', 0)
        masked_count = len(masked_fields)
        
        if global_score > 50:
            print("   ✅ Excellente performance - Système prêt pour production")
        elif global_score > 35:
            print("   ⚠️  Bonne performance - Quelques optimisations possibles")
        else:
            print("   🔧 Performance à améliorer - Vérifier patterns et OCR")
        
        if masked_count > 20:
            print("   🎭 Excellente détection des champs masqués")
        elif masked_count > 10:
            print("   🎭 Bonne détection des champs masqués")
        else:
            print("   🔍 Détection des champs masqués à améliorer")
        
        if scores.get('champs_obligatoires_detectes', 0) > 70:
            print("   🔥 Champs obligatoires bien détectés")
        else:
            print("   ⚠️  Améliorer la détection des champs obligatoires")
        
        # Sauvegarde avec timestamp
        timestamp = int(time.time())
        output_path = f"results/{pdf_name}_COMPLETE_ADVANCED_{timestamp}.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Résultat complet sauvegardé: {output_path}")
        
        return result, global_score, process_time
    
    else:
        print(f"❌ Erreur: {result.get('erreur')}")
        return None, 0, process_time

def analyze_field_coverage():
    """Analyse de la couverture des champs"""
    
    print(f"\n🔍 ANALYSE DE LA COUVERTURE DES CHAMPS:")
    print("="*60)
    
    extractor = CompleteAdvancedExtractor()
    
    print("📋 CHAMPS CONFIGURÉS PAR SECTION:")
    
    for section, fields in extractor.all_possible_fields.items():
        section_name = section.replace("informations_", "").upper()
        required_count = sum(1 for f in fields.values() if f.get("required", False))
        optional_count = len(fields) - required_count
        
        print(f"\n🏷️  {section_name}: {len(fields)} champs total")
        print(f"   🔥 Obligatoires: {required_count}")
        print(f"   📋 Optionnels: {optional_count}")
        
        # Afficher les champs obligatoires
        required_fields = [name for name, info in fields.items() if info.get("required", False)]
        if required_fields:
            print(f"   🔥 Champs critiques: {', '.join(required_fields[:5])}" + 
                  (f" (+{len(required_fields)-5})" if len(required_fields) > 5 else ""))

if __name__ == "__main__":
    try:
        result, score, time_taken = test_complete_advanced_system()
        analyze_field_coverage()
        
        print(f"\n🎉 TEST SYSTÈME COMPLET TERMINÉ!")
        if result:
            print(f"📊 Score final: {score:.1f}%")
            print(f"⏱️  Temps total: {time_taken:.1f}s")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()
