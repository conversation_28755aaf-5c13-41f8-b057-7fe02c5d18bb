#!/usr/bin/env python3
"""
Configuration pour l'extracteur de contrats avancé avec LLM
"""

import os
from typing import Dict, Any

# Configuration des modèles LLM
LLM_CONFIG = {
    # OpenAI Configuration
    "openai": {
        "api_key": os.getenv("OPENAI_API_KEY", "org-BDHN9DThUmlOFMxSWvI3p2jU"),
        "model": "gpt-4-turbo-preview",
        "temperature": 0.1,
        "max_tokens": 4000,
        "enabled": True
    },
    
    # DeepSeek Configuration
    "deepseek": {
        "api_key": os.getenv("DEEPSEEK_API_KEY", "***********************************"),
        "base_url": "https://api.deepseek.com/v1",
        "model": "deepseek-chat",
        "temperature": 0.1,
        "max_tokens": 4000,
        "enabled": True
    }
}

# Configuration OCR
OCR_CONFIG = {
    "tesseract_cmd": r'C:\Program Files\Tesseract-OCR\tesseract.exe',
    "languages": ['eng'],  # Utiliser seulement l'anglais qui fonctionne
    "config": r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ .,;:!?()[]{}/-+*=@#$%&',
    "dpi": 400,
    "image_format": 'PNG'
}

# Configuration des dossiers
FOLDERS_CONFIG = {
    "pdf_folder": 'contrat',
    "img_folder": 'images',
    "text_folder": 'ocr_texts',
    "result_folder": 'results'
}

# Configuration de validation
VALIDATION_CONFIG = {
    "min_field_length": 2,
    "max_field_lengths": {
        "nom": 25,
        "prenom": 25,
        "code_client": 12,
        "numero_cin": 8,
        "numero_rc": 15,
        "numero_tva": 10,
        "telephone": 15,
        "adresse": 100
    },
    "required_fields": {
        "informations_banque": ["nom_banque"],
        "informations_contrat": ["type_contrat"],
        "informations_client": ["nom", "prenom"],
        "informations_financieres": ["montant_principal"]
    }
}

# Patterns de validation par type de champ
FIELD_PATTERNS = {
    "numero_cin": r'^[0-9]{8}$',
    "numero_rc": r'^B[0-9]{9,12}$',
    "numero_tva": r'^[0-9]{6,}$',
    "date": r'^[0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4}$',
    "telephone": r'^[\+]?[0-9\s\.\-]{8,15}$',
    "montant": r'^[0-9\s,\.]+$'
}

# Messages de logging
LOG_MESSAGES = {
    "start_processing": "🚀 TRAITEMENT AVANCÉ LLM: {}",
    "ocr_phase": "📄 Phase 1: Extraction OCR avancée...",
    "openai_phase": "🧠 Phase 2: Analyse OpenAI GPT-4...",
    "deepseek_phase": "🧠 Phase 3: Analyse DeepSeek...",
    "fallback_phase": "🔧 Phase 4: Extraction de secours...",
    "merge_phase": "🔄 Phase 5: Fusion et validation...",
    "success": "✅ TRAITEMENT TERMINÉ: {}",
    "error": "❌ Erreur lors du traitement de {}: {}",
    "no_text": "Aucun texte extrait du PDF",
    "quality_excellent": "📈 QUALITÉ: EXCELLENTE",
    "quality_good": "📈 QUALITÉ: BONNE",
    "quality_average": "📈 QUALITÉ: MOYENNE",
    "quality_poor": "📈 QUALITÉ: FAIBLE"
}

def get_config() -> Dict[str, Any]:
    """Retourne la configuration complète"""
    return {
        "llm": LLM_CONFIG,
        "ocr": OCR_CONFIG,
        "folders": FOLDERS_CONFIG,
        "validation": VALIDATION_CONFIG,
        "patterns": FIELD_PATTERNS,
        "messages": LOG_MESSAGES
    }

def validate_config() -> bool:
    """Valide la configuration"""
    errors = []
    
    # Vérification des clés API
    if LLM_CONFIG["openai"]["api_key"] == "your-openai-api-key-here":
        errors.append("⚠️ Clé API OpenAI non configurée")
    
    if LLM_CONFIG["deepseek"]["api_key"] == "your-deepseek-api-key-here":
        errors.append("⚠️ Clé API DeepSeek non configurée")
    
    # Vérification de Tesseract
    if not os.path.exists(OCR_CONFIG["tesseract_cmd"]):
        errors.append("❌ Tesseract OCR non trouvé")
    
    # Vérification du dossier PDF
    if not os.path.exists(FOLDERS_CONFIG["pdf_folder"]):
        errors.append(f"❌ Dossier {FOLDERS_CONFIG['pdf_folder']} non trouvé")
    
    if errors:
        print("🔧 CONFIGURATION:")
        for error in errors:
            print(f"   {error}")
        return False
    
    print("✅ Configuration validée")
    return True

if __name__ == "__main__":
    validate_config()
