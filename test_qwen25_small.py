#!/usr/bin/env python3
"""
Test rapide avec Qwen 2.5-0.5B (plus petit, téléchargement rapide)
"""

import os
import json
import time
import logging
from typing import Dict
from detection_qwen_enhanced import QwenBTKContractExtractor

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickQwen25Extractor(QwenBTKContractExtractor):
    """Version rapide avec Qwen 2.5-0.5B"""
    
    def init_qwen_model(self):
        """Initialise directement Qwen 2.5-0.5B"""
        try:
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            logger.info("🧠 Chargement rapide Qwen 2.5-0.5B...")
            
            model_name = "Qwen/Qwen2.5-0.5B-Instruct"
            device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"🔧 Device: {device}")
            
            # Tokenizer
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                model_name, 
                trust_remote_code=True
            )
            
            # Modèle
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                device_map="auto" if device == "cuda" else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            if device == "cpu":
                self.qwen_model = self.qwen_model.to(device)
            
            logger.info("✅ Qwen 2.5-0.5B chargé avec succès!")
            
        except Exception as e:
            logger.error(f"❌ Erreur chargement Qwen 2.5: {str(e)}")
            self.qwen_model = None
            self.qwen_tokenizer = None

def test_quick_qwen25():
    """Test rapide avec le petit modèle"""
    
    print("🚀 === TEST RAPIDE QWEN 2.5-0.5B ===")
    print("="*50)
    
    # Initialisation
    start_time = time.time()
    extractor = QuickQwen25Extractor()
    init_time = time.time() - start_time
    
    print(f"⏱️  Initialisation: {init_time:.2f}s")
    
    # Test sur un fichier
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"\n🧪 Test sur: {test_file}")
    print("-" * 50)
    
    # Traitement
    start_process = time.time()
    result = extractor.process_contract_enhanced(pdf_path, pdf_name)
    process_time = time.time() - start_process
    
    print(f"\n⏱️  Temps total: {process_time:.2f}s")
    
    # Résultats rapides
    print(f"\n📊 RÉSULTATS QWEN 2.5-0.5B:")
    print("-" * 50)
    
    sections = ["informations_banque", "informations_contrat", "informations_client", "informations_signatures"]
    total_found = 0
    total_possible = 0
    
    for section in sections:
        section_name = section.replace("informations_", "").upper()
        section_data = result.get(section, {})
        
        found = sum(1 for v in section_data.values() if v and str(v).strip())
        possible = len(extractor.required_fields.get(section, []))
        
        total_found += found
        total_possible += possible
        
        print(f"🏷️  {section_name}: {found}/{possible} ({found/possible*100 if possible > 0 else 0:.1f}%)")
        
        # Afficher les 3 premiers champs trouvés
        count = 0
        for field, value in section_data.items():
            if value and str(value).strip() and count < 3:
                display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"   ✅ {field}: {display_value}")
                count += 1
    
    # Score global
    global_score = (total_found / total_possible * 100) if total_possible > 0 else 0
    print(f"\n⭐ SCORE GLOBAL: {global_score:.1f}%")
    
    # Champs masqués
    masked = result.get("champs_masques", [])
    print(f"🎭 Champs masqués: {len(masked)}")
    
    # Comparaison rapide
    print(f"\n📈 COMPARAISON:")
    print(f"   • Qwen 2.5-0.5B: {global_score:.1f}%")
    print(f"   • Version précédente: ~37%")
    
    if global_score > 37:
        print("   🎉 AMÉLIORATION avec Qwen 2.5!")
    else:
        print("   ⚠️  Résultats similaires")
    
    # Sauvegarde rapide
    output_path = f"results/{pdf_name}_QWEN25_SMALL.json"
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Sauvegardé: {output_path}")
    
    return result

def analyze_improvements():
    """Analyse les améliorations apportées par Qwen 2.5"""
    
    print(f"\n🔍 ANALYSE DES AMÉLIORATIONS QWEN 2.5:")
    print("="*50)
    
    improvements = [
        "🧠 Modèle plus récent et performant",
        "📝 Meilleure compréhension du contexte",
        "🎯 Extraction plus précise des champs",
        "🔧 Paramètres optimisés (temperature, top_p)",
        "📊 Support de contextes plus longs (8192 tokens)",
        "⚡ Génération plus stable et cohérente"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    print(f"\n💡 AVANTAGES QWEN 2.5 vs 1.5:")
    advantages = {
        "Architecture": "Transformer amélioré",
        "Données d'entraînement": "Plus récentes et diversifiées", 
        "Capacités multilingues": "Français/Arabe améliorés",
        "Précision JSON": "Génération plus fiable",
        "Vitesse": "Optimisations d'inférence"
    }
    
    for aspect, benefit in advantages.items():
        print(f"   📌 {aspect}: {benefit}")

if __name__ == "__main__":
    try:
        result = test_quick_qwen25()
        analyze_improvements()
        
        print(f"\n🎉 TEST QWEN 2.5-0.5B TERMINÉ!")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()
