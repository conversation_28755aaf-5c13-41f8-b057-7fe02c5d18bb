#!/usr/bin/env python3
"""
Test avec patterns regex améliorés seulement (sans Qwen pour rapidité)
"""

import os
import json
import time
from detection_qwen_enhanced import QwenBTKContractExtractor

class RegexOnlyExtractor(QwenBTKContractExtractor):
    """Version utilisant seulement les patterns regex améliorés"""
    
    def __init__(self):
        super().__init__()
        # Désactiver Qwen pour test rapide
        self.qwen_model = None
        self.qwen_tokenizer = None
    
    def init_qwen_model(self):
        """Désactiver Qwen pour test rapide"""
        self.qwen_model = None
        self.qwen_tokenizer = None

def test_regex_improvements():
    """Test des améliorations regex seules"""
    
    print("🚀 === TEST PATTERNS REGEX AMÉLIORÉS ===")
    print("="*50)
    
    # Initialisation rapide
    start_time = time.time()
    extractor = RegexOnlyExtractor()
    init_time = time.time() - start_time
    
    print(f"⏱️  Initialisation: {init_time:.2f}s")
    
    # Test sur un fichier
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"\n🧪 Test regex sur: {test_file}")
    print("-" * 50)
    
    # Traitement
    start_process = time.time()
    result = extractor.process_contract_enhanced(pdf_path, pdf_name)
    process_time = time.time() - start_process
    
    print(f"\n⏱️  Temps total: {process_time:.2f}s")
    
    # Analyse détaillée des résultats
    print(f"\n📊 RÉSULTATS PATTERNS REGEX AMÉLIORÉS:")
    print("="*50)
    
    sections_data = {}
    total_found = 0
    total_possible = 0
    
    for section in ["informations_banque", "informations_contrat", "informations_client", "informations_signatures"]:
        section_name = section.replace("informations_", "").upper()
        section_data = result.get(section, {})
        
        found = sum(1 for v in section_data.values() if v and str(v).strip())
        possible = len(extractor.required_fields.get(section, []))
        
        total_found += found
        total_possible += possible
        
        sections_data[section] = {
            "found": found,
            "possible": possible,
            "percentage": (found / possible * 100) if possible > 0 else 0
        }
        
        print(f"\n🏷️  {section_name}:")
        print(f"   📈 Complétude: {found}/{possible} ({sections_data[section]['percentage']:.1f}%)")
        
        # Afficher tous les champs trouvés
        if section_data:
            for field, value in section_data.items():
                if value and str(value).strip():
                    display_value = str(value)[:60] + "..." if len(str(value)) > 60 else str(value)
                    print(f"   ✅ {field}: {display_value}")
        
        # Afficher quelques champs manqués
        missing_fields = [f for f in extractor.required_fields.get(section, []) 
                         if not section_data.get(f)]
        if missing_fields:
            print(f"   ❌ Manqués: {', '.join(missing_fields[:5])}")
    
    # Score global
    global_score = (total_found / total_possible * 100) if total_possible > 0 else 0
    print(f"\n⭐ SCORE GLOBAL REGEX: {global_score:.1f}%")
    
    # Champs masqués
    masked = result.get("champs_masques", [])
    print(f"\n🎭 CHAMPS MASQUÉS DÉTECTÉS: {len(masked)}")
    
    # Grouper par confiance
    high_conf = [m for m in masked if m.get('confiance', 0) > 0.7]
    med_conf = [m for m in masked if 0.3 < m.get('confiance', 0) <= 0.7]
    low_conf = [m for m in masked if m.get('confiance', 0) <= 0.3]
    
    print(f"   🔥 Haute confiance (>70%): {len(high_conf)}")
    print(f"   ⚠️  Moyenne confiance (30-70%): {len(med_conf)}")
    print(f"   ❓ Faible confiance (<30%): {len(low_conf)}")
    
    # Exemples de masques détectés
    if high_conf:
        print(f"\n🔍 EXEMPLES MASQUES HAUTE CONFIANCE:")
        for i, mask in enumerate(high_conf[:5], 1):
            print(f"   {i}. {mask['champ']}: '{mask['masque']}' (conf: {mask.get('confiance', 0):.2f})")
    
    # Scores de qualité détaillés
    scores = result.get("scores_qualite", {})
    print(f"\n📊 SCORES DÉTAILLÉS:")
    print(f"   🎯 Score global: {scores.get('score_global', 0):.1f}%")
    print(f"   🏦 Banque: {scores.get('completude_banque', 0):.1f}%")
    print(f"   📄 Contrat: {scores.get('completude_contrat', 0):.1f}%")
    print(f"   👤 Client: {scores.get('completude_client', 0):.1f}%")
    print(f"   ✍️  Signatures: {scores.get('completude_signatures', 0):.1f}%")
    print(f"   🔥 Champs critiques: {scores.get('champs_critiques_detectes', 0):.1f}%")
    
    # Comparaison avec versions précédentes
    print(f"\n📈 COMPARAISON AVEC VERSIONS PRÉCÉDENTES:")
    comparisons = {
        "Version originale": "~22%",
        "Version améliorée": "~37%", 
        "Regex améliorés": f"{global_score:.1f}%"
    }
    
    for version, score in comparisons.items():
        status = "🎉" if "améliorés" in version and global_score > 37 else "📊"
        print(f"   {status} {version}: {score}")
    
    # Analyse des améliorations
    print(f"\n💡 AMÉLIORATIONS APPORTÉES:")
    improvements = [
        f"✅ Patterns regex ultra-précis pour tous les champs",
        f"✅ Détection contextuelle avancée",
        f"✅ Validation et scoring des matches",
        f"✅ Nettoyage intelligent des valeurs",
        f"✅ Support des nouveaux champs (CIN, taux, signatures)",
        f"✅ Détection robuste des champs masqués"
    ]
    
    for improvement in improvements:
        print(f"   {improvement}")
    
    # Sauvegarde
    output_path = f"results/{pdf_name}_REGEX_IMPROVED.json"
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Résultat sauvegardé: {output_path}")
    
    return result, global_score

def analyze_field_detection():
    """Analyse détaillée de la détection par champ"""
    
    print(f"\n🔍 ANALYSE DÉTAILLÉE PAR CHAMP:")
    print("="*50)
    
    # Champs critiques et leur importance
    critical_fields = {
        "nom_banque": "Identification de la banque",
        "type_contrat": "Type de contrat",
        "date_edition": "Date d'édition",
        "montant_principal": "Montant du prêt",
        "duree": "Durée du prêt",
        "taux_interet": "Taux d'intérêt",
        "nom_client": "Nom du client",
        "numero_cin": "Numéro CIN"
    }
    
    print("🔥 CHAMPS CRITIQUES:")
    for field, description in critical_fields.items():
        print(f"   • {field}: {description}")
    
    print(f"\n📋 NOUVEAUX CHAMPS AJOUTÉS:")
    new_fields = [
        "numero_cin (CIN 8 chiffres)",
        "date_signature", 
        "taux_semestriel",
        "taux_effectif_global",
        "commission_engagement",
        "frais_dossier",
        "lieu_signature",
        "representant_banque"
    ]
    
    for field in new_fields:
        print(f"   ✨ {field}")

if __name__ == "__main__":
    try:
        result, score = test_regex_improvements()
        analyze_field_detection()
        
        print(f"\n🎉 TEST REGEX TERMINÉ!")
        print(f"📊 Score final: {score:.1f}%")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()
