#!/usr/bin/env python3
"""
EXTRACTEUR COMPLET AVANCÉ - DÉTECTION DE TOUS LES CHAMPS
Détecte tous les champs importants même s'ils sont masqués ou vides
"""

import os
import json
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

class CompleteAdvancedExtractor:
    """Extracteur complet qui détecte tous les champs même masqués"""
    
    def __init__(self):
        self.ocr_config = r'--oem 3 --psm 6 -l fra+eng+ara'
        
        # TOUS les champs possibles dans un contrat bancaire BTK
        self.all_possible_fields = {
            "informations_banque": {
                "nom_banque": {"required": True, "type": "text"},
                "capital_social": {"required": True, "type": "amount"},
                "numero_rc": {"required": True, "type": "identifier"},
                "adresse": {"required": True, "type": "address"},
                "forme_juridique": {"required": True, "type": "text"},
                "telephone": {"required": False, "type": "phone"},
                "fax": {"required": False, "type": "phone"},
                "email": {"required": False, "type": "email"},
                "code_postal": {"required": False, "type": "number"},
                "ville": {"required": False, "type": "text"}
            },
            "informations_contrat": {
                "type_contrat": {"required": True, "type": "text"},
                "numero_contrat": {"required": True, "type": "identifier"},
                "date_edition": {"required": True, "type": "date"},
                "date_signature": {"required": True, "type": "date"},
                "date_debut": {"required": False, "type": "date"},
                "date_fin": {"required": False, "type": "date"},
                "date_limite_utilisation": {"required": False, "type": "date"},
                "montant_principal": {"required": True, "type": "amount"},
                "devise": {"required": False, "type": "text"},
                "duree": {"required": True, "type": "duration"},
                "taux_interet": {"required": True, "type": "percentage"},
                "taux_semestriel": {"required": False, "type": "percentage"},
                "taux_effectif_global": {"required": False, "type": "percentage"},
                "taux_debiteur": {"required": False, "type": "percentage"},
                "commission_gestion": {"required": True, "type": "percentage"},
                "commission_engagement": {"required": False, "type": "percentage"},
                "frais_dossier": {"required": False, "type": "amount"},
                "frais_garantie": {"required": False, "type": "amount"},
                "garanties": {"required": True, "type": "text"},
                "modalites_remboursement": {"required": False, "type": "text"},
                "periodicite_remboursement": {"required": False, "type": "text"},
                "nombre_echeances": {"required": False, "type": "number"},
                "montant_echeance": {"required": False, "type": "amount"}
            },
            "informations_client": {
                "code_client": {"required": True, "type": "identifier"},
                "nom_client": {"required": True, "type": "text"},
                "prenom_client": {"required": False, "type": "text"},
                "raison_sociale": {"required": False, "type": "text"},
                "numero_cin": {"required": True, "type": "cin"},
                "date_naissance": {"required": False, "type": "date"},
                "lieu_naissance": {"required": False, "type": "text"},
                "nationalite": {"required": False, "type": "text"},
                "adresse_client": {"required": True, "type": "address"},
                "code_postal_client": {"required": False, "type": "number"},
                "ville_client": {"required": False, "type": "text"},
                "telephone_client": {"required": False, "type": "phone"},
                "email_client": {"required": False, "type": "email"},
                "profession": {"required": False, "type": "text"},
                "secteur_activite": {"required": True, "type": "text"},
                "numero_rc_client": {"required": True, "type": "identifier"},
                "numero_compte": {"required": False, "type": "account"},
                "revenus": {"required": False, "type": "amount"},
                "situation_familiale": {"required": False, "type": "text"}
            },
            "informations_signatures": {
                "date_signature": {"required": True, "type": "date"},
                "lieu_signature": {"required": True, "type": "text"},
                "representant_banque": {"required": True, "type": "text"},
                "fonction_representant": {"required": False, "type": "text"},
                "signature_client": {"required": False, "type": "boolean"},
                "signature_banque": {"required": False, "type": "boolean"},
                "temoin_1": {"required": False, "type": "text"},
                "temoin_2": {"required": False, "type": "text"}
            },
            "informations_financieres": {
                "solde_compte": {"required": False, "type": "amount"},
                "decouvert_autorise": {"required": False, "type": "amount"},
                "plafond_credit": {"required": False, "type": "amount"},
                "assurance_credit": {"required": False, "type": "amount"},
                "cout_total_credit": {"required": False, "type": "amount"},
                "montant_total_du": {"required": False, "type": "amount"},
                "interets_totaux": {"required": False, "type": "amount"}
            },
            "conditions_particulieres": {
                "clause_resiliation": {"required": False, "type": "text"},
                "penalites_retard": {"required": False, "type": "percentage"},
                "conditions_remboursement_anticipe": {"required": False, "type": "text"},
                "juridiction_competente": {"required": False, "type": "text"},
                "loi_applicable": {"required": False, "type": "text"}
            }
        }

    def enhance_image_advanced(self, image: Image.Image) -> Image.Image:
        """Amélioration d'image avancée pour OCR optimal"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage adaptatif
        denoised = cv2.fastNlMeansDenoising(img_array, None, 10, 7, 21)
        
        # Amélioration du contraste CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphologie pour nettoyer
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return Image.fromarray(cleaned)

    def extract_text_with_positions(self, pdf_path: str, pdf_name: str) -> Tuple[str, List[Dict]]:
        """Extraction de texte avec positions des mots"""
        print(f"📄 Extraction OCR avancée pour {pdf_name}")
        
        # Créer dossiers
        for folder in ['images', 'ocr_texts']:
            os.makedirs(folder, exist_ok=True)
        
        try:
            pages = convert_from_path(pdf_path, dpi=300, fmt='png')
        except Exception as e:
            print(f"❌ Erreur conversion PDF: {str(e)}")
            return "", []
        
        all_text = ""
        all_words = []
        
        for i, page in enumerate(pages):
            page_num = i + 1
            print(f"🔍 Page {page_num}...")
            
            try:
                # Amélioration d'image
                enhanced_page = self.enhance_image_advanced(page)
                
                # Sauvegarde image
                img_path = f"images/{pdf_name}_page_{page_num}.png"
                enhanced_page.save(img_path, "PNG")
                
                # OCR texte
                text = pytesseract.image_to_string(enhanced_page, config=self.ocr_config)
                all_text += f"\n--- Page {page_num} ---\n{text}"
                
                # OCR avec données de position
                data = pytesseract.image_to_data(enhanced_page, config=self.ocr_config, output_type=pytesseract.Output.DICT)
                
                # Extraire les mots avec positions
                for j in range(len(data['text'])):
                    word = data['text'][j].strip()
                    if word and len(word) > 1:
                        all_words.append({
                            'text': word,
                            'page': page_num,
                            'x': data['left'][j],
                            'y': data['top'][j],
                            'width': data['width'][j],
                            'height': data['height'][j],
                            'confidence': data['conf'][j]
                        })
                
                print(f"✅ Page {page_num} traitée - {len([w for w in all_words if w['page'] == page_num])} mots")
            except Exception as e:
                print(f"❌ Erreur page {page_num}: {str(e)}")
        
        # Sauvegarde texte
        text_path = f"ocr_texts/{pdf_name}.txt"
        try:
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(all_text)
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {str(e)}")
        
        return all_text, all_words

    def get_ultra_advanced_patterns(self) -> Dict:
        """Patterns ultra-avancés pour détecter TOUS les champs"""
        return {
            "informations_banque": {
                "nom_banque": [
                    r"(?:La\s+)?BANQUE\s+TUNISO[\-\s]*KOWEITIENNE",
                    r"B\.T\.K\.?",
                    r"(?<![\w])BTK(?![\w])",
                    r"Banque\s+Tuniso[\-\s]*Koweitienne"
                ],
                "capital_social": [
                    r"capital\s+de\s+([\d\s,\.]+)\s*(?:de\s+)?dinars?",
                    r"au\s+capital\s+de\s+([\d\s,\.]+)\s*dinars?",
                    r"capital\s+social\s*[=:]\s*([\d\s,\.]+)",
                    r"Capital\s*[=:]\s*([\d\s,\.]+)"
                ],
                "numero_rc": [
                    r"R\.?C\.?\s*[=:]\s*([A-Z]?\d+)",
                    r"registre\s+(?:de\s+)?commerce\s*[=:]\s*([A-Z]?\d+)",
                    r"immatriculée.*?sous.*?n°\s*([A-Z]?\d+)",
                    r"RC\s*[=:]\s*([A-Z]?\d+)"
                ],
                "adresse": [
                    r"siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+(?:,\s*[^,\n\.]+)*)",
                    r"(\d+\s*bis[,\s]+Avenue\s+[^,\n\.]+)",
                    r"domiciliée?\s+(?:à\s+)?([^,\n\.]+)",
                    r"Adresse\s*[=:]\s*([^,\n\.]+)"
                ],
                "forme_juridique": [
                    r"(société\s+anonyme)",
                    r"(S\.A\.?)",
                    r"(SARL)",
                    r"(société\s+à\s+responsabilité\s+limitée)"
                ],
                "telephone": [
                    r"(?:Tél|Tel|Téléphone)\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})",
                    r"(\+216\s*[\d\s\-]{8,})",
                    r"Tél\s*[=:]\s*([\d\s\-\+\(\)]{8,})"
                ],
                "fax": [
                    r"Fax\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})",
                    r"Télécopie\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})"
                ],
                "email": [
                    r"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})",
                    r"E-mail\s*[=:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"
                ]
            },
            "informations_contrat": {
                "type_contrat": [
                    r"CONTRAT\s+DE\s+(PRET|PRÊT)",
                    r"contrat\s+de\s+(crédit|prêt)",
                    r"(prêt|crédit)\s+bancaire",
                    r"CREDIT\s+(BANCAIRE|PROFESSIONNEL)"
                ],
                "numero_contrat": [
                    r"Numéro\s+(?:de\s+)?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)",
                    r"Référence\s*[=:]\s*([A-Z0-9\-\/]+)",
                    r"N°\s*(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)",
                    r"Contrat\s+n°\s*([A-Z0-9\-\/]+)"
                ],
                "date_edition": [
                    r"Edité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"édité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"Date\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"Le\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "date_signature": [
                    r"signé\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"fait\s+le\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"signature\s*[=:]\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"Date\s+de\s+signature\s*[=:]\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "montant_principal": [
                    r"somme\s+principale\s+(?:de\s+)?([\d\s,\.]+)",
                    r"montant\s+(?:du\s+)?(?:crédit|prêt)\s*[=:]\s*([\d\s,\.]+)",
                    r"principal\s*[=:]\s*([\d\s,\.]+)",
                    r"Montant\s*[=:]\s*([\d\s,\.]+)"
                ],
                "devise": [
                    r"(dinars?)",
                    r"(TND)",
                    r"(DT)",
                    r"en\s+(dinars?\s+tunisiens?)"
                ],
                "duree": [
                    r"durée\s+de\s+(\d+\s+mois)",
                    r"pour\s+une\s+durée\s+de\s+(\d+\s+mois)",
                    r"remboursable\s+en\s+(\d+\s+mois)",
                    r"(\d+)\s+mois"
                ],
                "taux_interet": [
                    r"taux\s+d['\']intérêt\s*[=:]\s*([\d,\.]+\s*%?)",
                    r"intérêts\s+au\s+taux\s+(?:de\s+)?([\d,\.]+\s*%)",
                    r"taux\s*[=:]\s*([\d,\.]+\s*%)",
                    r"Taux\s+annuel\s*[=:]\s*([\d,\.]+\s*%)"
                ],
                "taux_semestriel": [
                    r"taux\s+semestriel\s+(?:du\s+marché\s+monétaire\s+)?(?:majoré\s+de\s+)?([\d,\.]+\s*%?)",
                    r"semestriel\s*[=:]\s*([\d,\.]+\s*%)",
                    r"TMM\s+majoré\s+de\s+([\d,\.]+\s*%)"
                ],
                "taux_effectif_global": [
                    r"taux\s+effectif\s+global\s*[=:]\s*([\d,\.]+\s*%)",
                    r"T\.?E\.?G\.?\s*[=:]\s*([\d,\.]+\s*%)",
                    r"TEG\s*[=:]\s*([\d,\.]+\s*%)"
                ],
                "commission_gestion": [
                    r"commission\s+de\s+gestion\s+de\s+([\d,\.]+\s*%)",
                    r"commission\s*[=:]\s*([\d,\.]+\s*%)",
                    r"frais\s+de\s+gestion\s*[=:]\s*([\d,\.]+\s*%)"
                ],
                "commission_engagement": [
                    r"commission\s+d['\']engagement\s*[=:]\s*([\d,\.]+\s*%)",
                    r"engagement\s*[=:]\s*([\d,\.]+\s*%)",
                    r"frais\s+d['\']engagement\s*[=:]\s*([\d,\.]+\s*%)"
                ],
                "garanties": [
                    r"garanties?\s*[=:]\s*([^\.]+)",
                    r"hypothèque\s+sur\s+([^\.]+)",
                    r"nantissement\s+(?:de\s+)?([^\.]+)",
                    r"Garantie\s*[=:]\s*([^\.]+)"
                ]
            },
            "informations_client": {
                "code_client": [
                    r"Code\s+Client\s*[=:]\s*([A-Z0-9X]{4,})",
                    r"Identifiant\s+Client\s*[=:]\s*([A-Z0-9X]{4,})",
                    r"PIN\s*[=:]\s*([A-Z0-9X]{4,})",
                    r"N°\s+Client\s*[=:]\s*([A-Z0-9X]{4,})"
                ],
                "nom_client": [
                    r"Et\s+([A-Z][A-Z\s&\-\.]+),?\s+dont\s+le\s+siège",
                    r"Emprunteuse?\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)",
                    r"Client\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)",
                    r"Souscripteur\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)",
                    r"Nom\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)"
                ],
                "numero_cin": [
                    r"CIN\s*[=:]\s*(\d{8})",
                    r"C\.I\.N\.?\s*[=:]\s*(\d{8})",
                    r"Carte\s+d['\']identité\s+(?:nationale\s+)?n°\s*(\d{8})",
                    r"N°\s*CIN\s*[=:]\s*(\d{8})",
                    r"numéro\s+CIN\s*[=:]\s*(\d{8})",
                    r"Identité\s*[=:]\s*(\d{8})"
                ],
                "adresse_client": [
                    r"dont\s+le\s+siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)",
                    r"domicilié(?:e)?\s+(?:à\s+)?([^,\n\.]+)",
                    r"adresse\s*[=:]\s*([^,\n\.]+)",
                    r"Adresse\s+Client\s*[=:]\s*([^,\n\.]+)"
                ],
                "numero_rc_client": [
                    r"immatriculé(?:e)?\s+au\s+(?:Centre\s+National\s+du\s+)?Registre\s+(?:des\s+Entreprises\s+)?sous\s+(?:l['\']identifiant\s+unique\s+)?n°\s*([A-Z0-9]+)",
                    r"R\.?C\.?\s+(?:client\s+)?[=:]\s*([A-Z0-9]+)",
                    r"Registre\s+Commerce\s*[=:]\s*([A-Z0-9]+)"
                ],
                "secteur_activite": [
                    r"Secteur\s+d['\']activité\s*[=:]\s*([^,\n\.]+)",
                    r"activité\s*[=:]\s*([^,\n\.]+)",
                    r"Secteur\s*[=:]\s*([^,\n\.]+)"
                ],
                "profession": [
                    r"profession\s*[=:]\s*([^,\n\.]+)",
                    r"métier\s*[=:]\s*([^,\n\.]+)",
                    r"fonction\s*[=:]\s*([^,\n\.]+)"
                ],
                "numero_compte": [
                    r"compte\s+n°\s*([A-Z0-9\-]+)",
                    r"numéro\s+de\s+compte\s*[=:]\s*([A-Z0-9\-]+)",
                    r"N°\s*compte\s*[=:]\s*([A-Z0-9\-]+)",
                    r"Compte\s*[=:]\s*([A-Z0-9\-]+)"
                ]
            }
        }

    def detect_all_fields_including_masked(self, text: str, words: List[Dict]) -> Dict:
        """Détecte TOUS les champs, même masqués ou vides"""
        print("🔍 Détection complète de tous les champs...")

        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_signatures": {},
            "informations_financieres": {},
            "conditions_particulieres": {}
        }

        # Nettoyage du texte
        clean_text = re.sub(r'\s+', ' ', text)

        # Obtenir tous les patterns
        patterns = self.get_ultra_advanced_patterns()

        # 1. Détection avec patterns regex
        for section, section_patterns in patterns.items():
            if section in result:
                for field, field_patterns in section_patterns.items():
                    best_value = self.find_best_field_value(clean_text, field_patterns, field)
                    if best_value:
                        result[section][field] = best_value

        # 2. Détection des champs masqués par analyse contextuelle
        masked_fields = self.detect_masked_fields_contextual(text, words)

        # 3. Détection des labels de champs même sans valeur
        field_labels = self.detect_field_labels(text)

        # 4. Fusion et enrichissement
        enriched_result = self.enrich_with_masked_and_labels(result, masked_fields, field_labels)

        return enriched_result

    def find_best_field_value(self, text: str, patterns: List[str], field: str) -> str:
        """Trouve la meilleure valeur pour un champ"""
        best_value = ""
        best_score = 0

        for pattern in patterns:
            matches = list(re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE))

            for match in matches:
                value = match.group(1).strip() if match.groups() else match.group(0).strip()

                # Validation et scoring
                if self.validate_field_value(field, value):
                    score = self.calculate_field_score(field, value, match.start(), text)

                    if score > best_score:
                        best_value = self.clean_field_value(field, value)
                        best_score = score

        return best_value

    def validate_field_value(self, field: str, value: str) -> bool:
        """Validation avancée des valeurs de champs"""
        if not value or len(value.strip()) < 1:
            return False

        # Validations spécifiques par type
        validators = {
            "date": lambda v: bool(re.match(r'\d{1,2}\/\d{1,2}\/\d{4}', v)),
            "cin": lambda v: bool(re.match(r'\d{8}', v)) and len(v) == 8,
            "identifier": lambda v: len(v) >= 3 and bool(re.match(r'[A-Z0-9\-\/]+', v)),
            "percentage": lambda v: bool(re.search(r'\d+', v)),
            "amount": lambda v: bool(re.search(r'\d+', v)),
            "phone": lambda v: bool(re.search(r'\d{6,}', v.replace(' ', '').replace('-', ''))),
            "email": lambda v: bool(re.match(r'[^@]+@[^@]+\.[^@]+', v))
        }

        # Obtenir le type du champ
        field_type = self.get_field_type(field)
        if field_type in validators:
            return validators[field_type](value)

        # Validation générale
        return not re.match(r'^[^a-zA-Z0-9À-ÿ]+$', value)

    def get_field_type(self, field: str) -> str:
        """Obtient le type d'un champ"""
        for section, fields in self.all_possible_fields.items():
            if field in fields:
                return fields[field]["type"]
        return "text"

    def calculate_field_score(self, field: str, value: str, position: int, text: str) -> float:
        """Calcule le score de qualité d'un match"""
        score = 1.0

        # Bonus pour longueur appropriée
        length_ranges = {
            "nom_banque": (10, 50),
            "numero_rc": (3, 15),
            "date_edition": (8, 12),
            "montant_principal": (3, 20),
            "duree": (5, 15),
            "nom_client": (5, 50),
            "code_client": (4, 20),
            "numero_cin": (8, 8)
        }

        if field in length_ranges:
            min_len, max_len = length_ranges[field]
            if min_len <= len(value) <= max_len:
                score += 0.3
            elif len(value) < min_len or len(value) > max_len * 2:
                score -= 0.5

        # Bonus pour contexte
        context_start = max(0, position - 100)
        context_end = min(len(text), position + len(value) + 100)
        context = text[context_start:context_end].lower()

        # Mots-clés contextuels
        context_keywords = {
            "nom_banque": ["banque", "btk", "tuniso"],
            "numero_rc": ["registre", "commerce", "immatriculée"],
            "date_edition": ["edité", "date"],
            "montant_principal": ["somme", "principal", "montant"],
            "duree": ["durée", "mois"],
            "taux_interet": ["taux", "intérêt"],
            "nom_client": ["client", "emprunteuse", "souscripteur"],
            "numero_cin": ["cin", "identité", "carte"]
        }

        if field in context_keywords:
            keyword_count = sum(1 for kw in context_keywords[field] if kw in context)
            score += keyword_count * 0.2

        return max(0, score)

    def clean_field_value(self, field: str, value: str) -> str:
        """Nettoie la valeur d'un champ"""
        clean_value = value.strip()
        clean_value = re.sub(r'\s+', ' ', clean_value)

        # Nettoyages spécifiques
        if field == "nom_banque":
            clean_value = re.sub(r'^(?:La\s+)?', '', clean_value, flags=re.IGNORECASE)
        elif field in ["adresse", "adresse_client"]:
            clean_value = re.sub(r'^(?:à\s+)?', '', clean_value, flags=re.IGNORECASE)
        elif field == "forme_juridique":
            clean_value = clean_value.lower()
        elif field in ["taux_interet", "commission_gestion", "taux_semestriel"]:
            if not clean_value.endswith('%') and re.search(r'\d', clean_value):
                clean_value += '%'
        elif field == "numero_cin":
            clean_value = re.sub(r'[^\d]', '', clean_value)

        return clean_value

    def detect_masked_fields_contextual(self, text: str, words: List[Dict]) -> List[Dict]:
        """Détection contextuelle des champs masqués"""
        masked_fields = []

        if not words:
            return masked_fields

        # Grouper les mots par ligne
        lines = self.group_words_by_line(words)

        # Rechercher les champs masqués près des labels
        for i, line in enumerate(lines):
            line_text = " ".join(w["text"] for w in line)

            # Chercher les labels de champs
            for section, fields in self.all_possible_fields.items():
                for field, field_info in fields.items():
                    # Patterns de labels pour ce champ
                    label_patterns = self.get_label_patterns(field)

                    for pattern in label_patterns:
                        if re.search(pattern, line_text, re.IGNORECASE):
                            # Chercher des masques dans cette ligne et les suivantes
                            masked_in_context = self.find_masked_near_label(
                                lines, i, field, field_info.get("required", False)
                            )
                            masked_fields.extend(masked_in_context)

        return masked_fields

    def get_label_patterns(self, field: str) -> List[str]:
        """Obtient les patterns de labels pour un champ"""
        label_patterns = {
            "nom_banque": [r"banque", r"établissement"],
            "capital_social": [r"capital", r"capital social"],
            "numero_rc": [r"r\.?c\.?", r"registre", r"commerce"],
            "date_edition": [r"edité", r"date"],
            "montant_principal": [r"somme", r"montant", r"principal"],
            "numero_cin": [r"cin", r"c\.i\.n", r"carte.*identité"],
            "code_client": [r"code.*client", r"identifiant"],
            "taux_interet": [r"taux", r"intérêt"],
            "duree": [r"durée", r"période"],
            "garanties": [r"garantie", r"hypothèque", r"nantissement"]
        }

        return label_patterns.get(field, [field.replace("_", r"[\s_]")])

    def group_words_by_line(self, words: List[Dict]) -> List[List[Dict]]:
        """Groupe les mots par ligne avec tolérance"""
        if not words:
            return []

        # Trier par page, puis y, puis x
        sorted_words = sorted(words, key=lambda w: (w['page'], w['y'], w['x']))

        lines = []
        current_line = []
        current_y = sorted_words[0]['y']

        for word in sorted_words:
            tolerance = max(word['height'] * 0.4, 15)

            if abs(word['y'] - current_y) <= tolerance:
                current_line.append(word)
            else:
                if current_line:
                    current_line.sort(key=lambda w: w['x'])
                    lines.append(current_line)
                current_line = [word]
                current_y = word['y']

        if current_line:
            current_line.sort(key=lambda w: w['x'])
            lines.append(current_line)

        return lines

    def find_masked_near_label(self, lines: List[List[Dict]], start_line: int, field: str, is_required: bool) -> List[Dict]:
        """Trouve les champs masqués près d'un label"""
        masked_fields = []

        # Chercher dans la ligne courante et les 2 suivantes
        for i in range(start_line, min(start_line + 3, len(lines))):
            for word in lines[i]:
                if self.is_masked_value(word["text"]):
                    confidence = self.calculate_mask_confidence(word["text"], is_required)

                    masked_fields.append({
                        "champ": field,
                        "valeur_masquee": word["text"],
                        "position": {
                            "page": word["page"],
                            "x": word["x"],
                            "y": word["y"],
                            "width": word["width"],
                            "height": word["height"]
                        },
                        "confiance": confidence,
                        "obligatoire": is_required,
                        "type": self.get_field_type(field)
                    })

        return masked_fields

    def is_masked_value(self, text: str) -> bool:
        """Vérifie si un texte représente une valeur masquée"""
        if len(text) < 2:
            return False

        # Patterns de masquage étendus
        masked_patterns = [
            r'^X{2,}$',           # XXXX
            r'^\*{2,}$',          # ****
            r'^•{2,}$',           # ••••
            r'^_{3,}$',           # ___
            r'^#{2,}$',           # ####
            r'^[X*•_#\s]{3,}$',   # Combinaisons
            r'^xxx+$',            # xxx
            r'^XXX+$',            # XXX
            r'^\[.*\]$',          # [masqué]
            r'^<.*>$',            # <masqué>
            r'^\{.*\}$',          # {masqué}
            r'^\.{3,}$',          # ...
            r'^-{3,}$'            # ---
        ]

        for pattern in masked_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True

        # Densité de caractères de masquage
        mask_chars = sum(1 for c in text if c in 'X*•_#.-')
        if len(text) > 0 and mask_chars / len(text) > 0.7:
            return True

        return False

    def calculate_mask_confidence(self, text: str, is_required: bool) -> float:
        """Calcule la confiance qu'un texte est masqué"""
        if not text:
            return 0.0

        # Facteurs de base
        mask_chars = sum(1 for c in text if c in 'X*•_#.-')
        length_factor = min(len(text) / 10, 1.0)
        density_factor = mask_chars / len(text) if len(text) > 0 else 0

        # Bonus pour patterns typiques
        pattern_bonus = 0.3 if re.match(r'^[X*•_#]{3,}$', text) else 0

        # Bonus si champ obligatoire
        required_bonus = 0.2 if is_required else 0

        confidence = (density_factor * 0.5 + length_factor * 0.2 + pattern_bonus + required_bonus)
        return min(confidence, 1.0)

    def detect_field_labels(self, text: str) -> Dict:
        """Détecte les labels de champs même sans valeur"""
        field_labels = {}

        # Labels courants dans les contrats BTK
        common_labels = {
            "Code Client": "code_client",
            "Nom du Client": "nom_client",
            "CIN": "numero_cin",
            "Numéro de contrat": "numero_contrat",
            "Date d'édition": "date_edition",
            "Montant principal": "montant_principal",
            "Durée": "duree",
            "Taux d'intérêt": "taux_interet",
            "Commission de gestion": "commission_gestion",
            "Garanties": "garanties",
            "Secteur d'activité": "secteur_activite",
            "R.C.": "numero_rc"
        }

        for label, field in common_labels.items():
            pattern = rf'\b{re.escape(label)}\b'
            if re.search(pattern, text, re.IGNORECASE):
                field_labels[field] = {
                    "label_detecte": label,
                    "present": True,
                    "valeur_trouvee": False
                }

        return field_labels

    def enrich_with_masked_and_labels(self, result: Dict, masked_fields: List[Dict], field_labels: Dict) -> Dict:
        """Enrichit les résultats avec les champs masqués et labels"""

        # Ajouter les informations sur les champs masqués
        result["champs_masques"] = masked_fields
        result["labels_detectes"] = field_labels

        # Marquer les champs détectés mais masqués
        for masked in masked_fields:
            field = masked["champ"]

            # Trouver la section du champ
            for section, fields in self.all_possible_fields.items():
                if field in fields:
                    if section not in result:
                        result[section] = {}

                    # Si pas de valeur trouvée, marquer comme masqué
                    if not result[section].get(field):
                        result[section][field] = f"[MASQUÉ: {masked['valeur_masquee']}]"
                    break

        return result

    def calculate_comprehensive_scores(self, result: Dict) -> Dict:
        """Calcul des scores complets"""
        scores = {
            "score_global": 0,
            "completude_banque": 0,
            "completude_contrat": 0,
            "completude_client": 0,
            "completude_signatures": 0,
            "completude_financieres": 0,
            "champs_detectes": 0,
            "champs_masques": 0,
            "champs_obligatoires_detectes": 0
        }

        total_detected = 0
        total_possible = 0
        total_required_detected = 0
        total_required = 0

        # Calcul par section
        for section, fields in self.all_possible_fields.items():
            section_key = f"completude_{section.split('_')[1]}"

            detected = 0
            required_detected = 0
            required_count = 0

            for field, field_info in fields.items():
                is_required = field_info.get("required", False)
                has_value = bool(result.get(section, {}).get(field))

                if has_value:
                    detected += 1
                    total_detected += 1

                    if is_required:
                        required_detected += 1
                        total_required_detected += 1

                if is_required:
                    required_count += 1
                    total_required += 1

                total_possible += 1

            scores[section_key] = (detected / len(fields)) * 100 if fields else 0

        # Scores globaux
        scores["score_global"] = (total_detected / total_possible) * 100 if total_possible > 0 else 0
        scores["champs_detectes"] = total_detected
        scores["champs_masques"] = len(result.get("champs_masques", []))
        scores["champs_obligatoires_detectes"] = (total_required_detected / total_required) * 100 if total_required > 0 else 0

        return scores

    def process_contract_complete(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement complet d'un contrat avec détection de tous les champs"""
        print(f"🚀 TRAITEMENT COMPLET AVANCÉ: {pdf_name}")

        try:
            # 1. Extraction OCR avec positions
            print("📄 Phase 1: Extraction OCR avec positions...")
            ocr_text, word_positions = self.extract_text_with_positions(pdf_path, pdf_name)

            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")

            print(f"📝 Texte extrait: {len(ocr_text)} caractères, {len(word_positions)} mots")

            # 2. Détection complète de tous les champs
            print("🔍 Phase 2: Détection complète de tous les champs...")
            complete_results = self.detect_all_fields_including_masked(ocr_text, word_positions)

            # 3. Calcul des scores complets
            print("📊 Phase 3: Calcul des scores complets...")
            comprehensive_scores = self.calculate_comprehensive_scores(complete_results)

            # 4. Assemblage final
            final_result = {
                **complete_results,
                "scores_qualite": comprehensive_scores,
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Complete Advanced Field Detector",
                    "ocr_text_length": len(ocr_text),
                    "total_words_detected": len(word_positions),
                    "champs_detectes": comprehensive_scores.get("champs_detectes", 0),
                    "champs_masques": comprehensive_scores.get("champs_masques", 0),
                    "quality_score_global": comprehensive_scores.get("score_global", 0)
                }
            }

            # 5. Affichage du résumé
            self.display_complete_summary(final_result)

            print(f"✅ TRAITEMENT COMPLET TERMINÉ!")
            return final_result

        except Exception as e:
            print(f"❌ Erreur traitement complet {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf"
            }

    def display_complete_summary(self, result: Dict):
        """Affichage du résumé complet"""
        print("\n📊 === RÉSUMÉ COMPLET DE L'EXTRACTION ===")

        scores = result.get("scores_qualite", {})

        # Statistiques globales
        print(f"🎯 Score global: {scores.get('score_global', 0):.1f}%")
        print(f"📋 Champs détectés: {scores.get('champs_detectes', 0)}")
        print(f"🎭 Champs masqués: {scores.get('champs_masques', 0)}")
        print(f"🔥 Champs obligatoires: {scores.get('champs_obligatoires_detectes', 0):.1f}%")

        # Par section
        sections = ["banque", "contrat", "client", "signatures", "financieres"]
        for section in sections:
            score = scores.get(f"completude_{section}", 0)
            print(f"📊 {section.upper()}: {score:.1f}%")

def main():
    """Fonction principale"""
    print("🎯 === EXTRACTEUR COMPLET AVANCÉ BTK ===")

    # Initialisation
    extractor = CompleteAdvancedExtractor()

    # Vérification dossiers
    if not os.path.exists('contrat'):
        print("❌ Dossier 'contrat' non trouvé")
        return

    # Création dossiers sortie
    for folder in ['images', 'ocr_texts', 'results']:
        os.makedirs(folder, exist_ok=True)

    # Recherche PDFs
    pdf_files = [f for f in os.listdir('contrat')
                 if f.lower().endswith('.pdf') and not f.startswith('~')]

    if not pdf_files:
        print("❌ Aucun PDF trouvé")
        return

    print(f"📁 {len(pdf_files)} fichier(s) PDF trouvé(s)")

    # Traitement
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"\n{'='*80}")
        print(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        print(f"{'='*80}")

        pdf_path = os.path.join('contrat', pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]

        # Traitement complet
        result = extractor.process_contract_complete(pdf_path, pdf_name)

        # Sauvegarde
        output_path = f"results/{pdf_name}_COMPLETE_ADVANCED.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        print(f"💾 Sauvegardé: {output_path}")

if __name__ == "__main__":
    main()
