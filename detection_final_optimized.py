#!/usr/bin/env python3
"""
EXTRACTEUR DE CONTRATS BANCAIRES BTK - VERSION FINALE OPTIMISÉE
Combinaison OCR + Qwen avec précision maximale pour tous les champs obligatoires
"""

import os
import json
import pytesseract
from pathlib import Path
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Any, Tuple
from dotenv import load_dotenv
from bs4 import BeautifulSoup

# Imports pour Qwen
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False
    logging.warning("⚠️ Transformers non disponible - Qwen désactivé")

# Charger les variables d'environnement
load_dotenv()

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Dossiers
PDF_FOLDER = 'contrat'
IMG_FOLDER = 'images'
HOCR_FOLDER = 'hocr'
RESULT_FOLDER = 'results'
TEXT_FOLDER = 'ocr_texts'

class FinalOptimizedBTKExtractor:
    """Extracteur final optimisé pour contrats BTK"""
    
    def __init__(self):
        # Configuration OCR optimisée
        self.ocr_config = r'--oem 3 --psm 6 -l fra+eng+ara'
        
        # Champs obligatoires avec priorités
        self.required_fields = {
            "informations_banque": {
                "nom_banque": {"priority": 1, "patterns": ["BANQUE TUNISO-KOWEITIENNE", "B.T.K", "BTK"]},
                "capital_social": {"priority": 2, "patterns": ["capital", "dinars"]},
                "numero_rc": {"priority": 3, "patterns": ["R.C.", "registre"]},
                "adresse": {"priority": 2, "patterns": ["siège social", "Avenue"]},
                "forme_juridique": {"priority": 3, "patterns": ["société anonyme"]},
                "telephone": {"priority": 3, "patterns": ["tél", "phone"]}
            },
            "informations_contrat": {
                "type_contrat": {"priority": 1, "patterns": ["CONTRAT DE PRET", "prêt"]},
                "numero_contrat": {"priority": 1, "patterns": ["Numéro", "Référence"]},
                "date_edition": {"priority": 1, "patterns": ["Edité le", "Date"]},
                "montant_principal": {"priority": 1, "patterns": ["somme principale", "montant"]},
                "duree": {"priority": 1, "patterns": ["durée", "mois"]},
                "taux_interet": {"priority": 1, "patterns": ["taux", "intérêt"]},
                "commission_gestion": {"priority": 2, "patterns": ["commission"]},
                "garanties": {"priority": 3, "patterns": ["garantie", "hypothèque"]}
            },
            "informations_client": {
                "code_client": {"priority": 1, "patterns": ["Code Client", "PIN"]},
                "nom_client": {"priority": 1, "patterns": ["Client", "Emprunteuse"]},
                "adresse_client": {"priority": 2, "patterns": ["siège social", "domicilié"]},
                "secteur_activite": {"priority": 3, "patterns": ["secteur", "activité"]},
                "numero_rc_client": {"priority": 2, "patterns": ["R.C.", "immatriculé"]}
            }
        }
        
        # Initialisation du modèle Qwen
        self.qwen_model = None
        self.qwen_tokenizer = None
        self.init_qwen_model()

    def init_qwen_model(self):
        """Initialise le modèle Qwen optimisé"""
        if not QWEN_AVAILABLE:
            logger.warning("⚠️ Qwen non disponible - utilisation OCR seul")
            return
            
        try:
            logger.info("🧠 Chargement du modèle Qwen optimisé...")
            
            model_name = "Qwen/Qwen1.5-0.5B-Chat"
            device = "cuda" if torch.cuda.is_available() else "cpu"
            
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                model_name, 
                trust_remote_code=True,
                pad_token='<|endoftext|>'
            )
            
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                device_map="auto" if device == "cuda" else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            if device == "cpu":
                self.qwen_model = self.qwen_model.to(device)
            
            logger.info("✅ Modèle Qwen optimisé chargé avec succès")
            
        except Exception as e:
            logger.error(f"❌ Erreur chargement Qwen: {str(e)}")
            self.qwen_model = None
            self.qwen_tokenizer = None

    def enhance_image_advanced(self, image: Image.Image) -> Image.Image:
        """Amélioration d'image avancée pour OCR optimal"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage gaussien
        denoised = cv2.GaussianBlur(img_array, (3, 3), 0)
        
        # Amélioration du contraste adaptatif
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation Otsu
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphologie pour nettoyer
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return Image.fromarray(cleaned)

    def extract_text_optimized(self, pdf_path: str, pdf_name: str) -> Tuple[str, List[Dict]]:
        """Extraction de texte optimisée"""
        logger.info(f"📄 Extraction OCR optimisée pour {pdf_name}")
        
        # Créer les dossiers
        for folder in [IMG_FOLDER, HOCR_FOLDER, TEXT_FOLDER]:
            os.makedirs(folder, exist_ok=True)
        
        # Conversion PDF avec DPI optimal
        try:
            pages = convert_from_path(pdf_path, dpi=300, fmt='png')
        except Exception as e:
            logger.error(f"❌ Erreur conversion PDF: {str(e)}")
            return "", []
        
        all_text = ""
        all_words = []
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Traitement page {page_num}...")
            
            try:
                # Amélioration d'image
                enhanced_page = self.enhance_image_advanced(page)
                
                # Sauvegarde
                img_path = f"{IMG_FOLDER}/{pdf_name}_page_{page_num}.png"
                enhanced_page.save(img_path, "PNG")
                
                # OCR avec configuration optimisée
                text = pytesseract.image_to_string(enhanced_page, config=self.ocr_config)
                all_text += f"\n--- Page {page_num} ---\n{text}"
                
                # HOCR pour positions
                hocr_data = pytesseract.image_to_pdf_or_hocr(
                    enhanced_page, extension='hocr', config=self.ocr_config
                )
                
                # Sauvegarde HOCR
                hocr_path = f"{HOCR_FOLDER}/{pdf_name}_page_{page_num}.hocr"
                with open(hocr_path, 'wb') as f:
                    f.write(hocr_data)
                    
                # Analyse HOCR
                page_words = self.parse_hocr_optimized(hocr_data, page_num)
                all_words.extend(page_words)
                
                logger.info(f"✅ Page {page_num} traitée - {len(page_words)} mots détectés")
            except Exception as e:
                logger.error(f"❌ Erreur traitement page {page_num}: {str(e)}")
        
        # Sauvegarde du texte
        text_path = f"{TEXT_FOLDER}/{pdf_name}.txt"
        try:
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(all_text)
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde texte: {str(e)}")
        
        return all_text, all_words

    def parse_hocr_optimized(self, hocr_data: bytes, page_num: int) -> List[Dict]:
        """Analyse HOCR optimisée"""
        words = []
        try:
            soup = BeautifulSoup(hocr_data, 'html.parser')
            word_elements = soup.find_all(class_='ocrx_word')
            
            for word in word_elements:
                text = word.get_text().strip()
                if not text or len(text) < 2:
                    continue
                    
                title = word.get('title', '')
                bbox = re.search(r'bbox (\d+) (\d+) (\d+) (\d+)', title)
                if not bbox:
                    continue
                    
                x1, y1, x2, y2 = map(int, bbox.groups())
                
                words.append({
                    "text": text,
                    "page": page_num,
                    "x": x1,
                    "y": y1,
                    "width": x2 - x1,
                    "height": y2 - y1,
                    "confidence": self.extract_confidence(title)
                })
        except Exception as e:
            logger.error(f"❌ Erreur analyse HOCR: {str(e)}")
        
        return words

    def extract_confidence(self, title: str) -> float:
        """Extrait la confiance OCR du titre HOCR"""
        conf_match = re.search(r'x_wconf (\d+)', title)
        if conf_match:
            return float(conf_match.group(1)) / 100.0
        return 0.8  # Confiance par défaut

    def extract_with_ultra_precision(self, text: str) -> Dict:
        """Extraction ultra-précise avec patterns avancés"""
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {}
        }

        # Nettoyage du texte
        clean_text = self.clean_text_advanced(text)

        # Patterns ultra-précis pour chaque section
        patterns = {
            "informations_banque": {
                "nom_banque": [
                    r"(?:La\s+)?BANQUE\s+TUNISO[\-\s]*KOWEITIENNE",
                    r"B\.T\.K\.?",
                    r"(?<![\w])BTK(?![\w])"
                ],
                "capital_social": [
                    r"capital\s+de\s+([\d\s,\.]+)\s*(?:de\s+)?dinars?",
                    r"au\s+capital\s+de\s+([\d\s,\.]+)\s*dinars?"
                ],
                "numero_rc": [
                    r"R\.?C\.?\s*[=:]\s*([A-Z]?\d+)",
                    r"registre\s+(?:de\s+)?commerce\s*[=:]\s*([A-Z]?\d+)"
                ],
                "adresse": [
                    r"siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+(?:,\s*[^,\n\.]+)*)",
                    r"(\d+\s*bis[,\s]+Avenue\s+[^,\n\.]+)"
                ],
                "forme_juridique": [
                    r"(société\s+anonyme)",
                    r"(S\.A\.?)"
                ],
                "telephone": [
                    r"(?:Tél|Tel|Téléphone)\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})"
                ]
            },
            "informations_contrat": {
                "type_contrat": [
                    r"CONTRAT\s+DE\s+(PRET|PRÊT)",
                    r"contrat\s+de\s+(crédit|prêt)"
                ],
                "numero_contrat": [
                    r"Numéro\s+(?:de\s+)?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)",
                    r"Référence\s*[=:]\s*([A-Z0-9\-\/]+)"
                ],
                "date_edition": [
                    r"Edité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"Date\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "montant_principal": [
                    r"somme\s+principale\s+(?:de\s+)?([\d\s,\.]+)",
                    r"montant\s+(?:du\s+)?(?:crédit|prêt)\s*[=:]\s*([\d\s,\.]+)"
                ],
                "duree": [
                    r"durée\s+de\s+(\d+\s+mois)",
                    r"pour\s+une\s+durée\s+de\s+(\d+\s+mois)"
                ],
                "taux_interet": [
                    r"taux\s+semestriel\s+(?:du\s+marché\s+monétaire\s+)?(?:majoré\s+de\s+)?([\d,\.]+\s*%?)",
                    r"intérêts\s+au\s+taux\s+(?:de\s+)?([\d,\.]+\s*%)"
                ],
                "commission_gestion": [
                    r"commission\s+de\s+gestion\s+de\s+([\d,\.]+\s*%)",
                    r"commission\s*[=:]\s*([\d,\.]+\s*%)"
                ],
                "garanties": [
                    r"garanties?\s*[=:]\s*([^\.]+)",
                    r"hypothèque\s+sur\s+([^\.]+)"
                ]
            },
            "informations_client": {
                "code_client": [
                    r"Code\s+Client\s*[=:]\s*([A-Z0-9X]{4,})",
                    r"PIN\s*[=:]\s*([A-Z0-9X]{4,})"
                ],
                "nom_client": [
                    r"Et\s+([A-Z][A-Z\s&\-\.]+),?\s+dont\s+le\s+siège",
                    r"Emprunteuse?\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)",
                    r"Client\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)"
                ],
                "adresse_client": [
                    r"dont\s+le\s+siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)",
                    r"domicilié(?:e)?\s+(?:à\s+)?([^,\n\.]+)"
                ],
                "numero_rc_client": [
                    r"immatriculé(?:e)?\s+au\s+(?:Centre\s+National\s+du\s+)?Registre\s+(?:des\s+Entreprises\s+)?sous\s+(?:l['\']identifiant\s+unique\s+)?n°\s*([A-Z0-9]+)"
                ],
                "secteur_activite": [
                    r"Secteur\s+d['\']activité\s*[=:]\s*([^,\n\.]+)"
                ]
            }
        }

        # Application des patterns avec scoring
        for section, section_patterns in patterns.items():
            for field, field_patterns in section_patterns.items():
                best_match = self.find_best_match(clean_text, field_patterns, field)
                if best_match:
                    result[section][field] = best_match

        return result

    def clean_text_advanced(self, text: str) -> str:
        """Nettoyage avancé du texte"""
        # Suppression des caractères de contrôle
        clean_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', ' ', text)
        clean_text = re.sub(r'\s+', ' ', clean_text)

        # Corrections OCR communes
        corrections = {
            r'B\.T\.K\.': 'B.T.K.',
            r'BANQUE\s+TUNISO[\-\s]*KOWEITIENNE': 'BANQUE TUNISO-KOWEITIENNE',
            r'societe\s+anonyme': 'société anonyme',
            r'Edite\s+le': 'Edité le',
            r'duree\s+de': 'durée de',
            r'R\.C\.': 'R.C.',
            r'siege\s+social': 'siège social'
        }

        for pattern, replacement in corrections.items():
            clean_text = re.sub(pattern, replacement, clean_text, flags=re.IGNORECASE)

        return clean_text

    def find_best_match(self, text: str, patterns: List[str], field: str) -> str:
        """Trouve le meilleur match pour un champ"""
        best_match = None
        best_score = 0

        for pattern in patterns:
            matches = list(re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE))

            for match in matches:
                value = match.group(1).strip() if match.groups() else match.group(0).strip()

                # Validation et scoring
                if self.validate_field_value_advanced(field, value):
                    score = self.calculate_match_score(field, value, match.start(), text)

                    if score > best_score:
                        best_match = self.clean_field_value(field, value)
                        best_score = score

        return best_match

    def validate_field_value_advanced(self, field: str, value: str) -> bool:
        """Validation avancée des valeurs"""
        if not value or len(value.strip()) < 2:
            return False

        validators = {
            "date_edition": lambda v: bool(re.match(r'\d{1,2}\/\d{1,2}\/\d{4}', v)),
            "numero_rc": lambda v: bool(re.match(r'[A-Z]?\d+', v)) and len(v) >= 3,
            "numero_rc_client": lambda v: bool(re.match(r'[A-Z]?\d+', v)) and len(v) >= 3,
            "taux_interet": lambda v: bool(re.search(r'\d+', v)),
            "duree": lambda v: bool(re.search(r'\d+', v)),
            "montant_principal": lambda v: bool(re.search(r'\d+', v)),
            "code_client": lambda v: len(v) >= 4 and bool(re.match(r'[A-Z0-9X]+', v)),
            "telephone": lambda v: bool(re.search(r'\d{6,}', v.replace(' ', '').replace('-', '')))
        }

        if field in validators:
            return validators[field](value)

        # Validation générale
        if re.match(r'^[^a-zA-Z0-9À-ÿ]+$', value):
            return False

        return True

    def calculate_match_score(self, field: str, value: str, position: int, text: str) -> float:
        """Calcule le score d'un match"""
        score = 1.0

        # Bonus pour longueur appropriée
        length_ranges = {
            "nom_banque": (10, 50),
            "numero_rc": (3, 15),
            "date_edition": (8, 12),
            "montant_principal": (3, 20),
            "duree": (5, 15),
            "nom_client": (5, 50),
            "code_client": (4, 20)
        }

        if field in length_ranges:
            min_len, max_len = length_ranges[field]
            if min_len <= len(value) <= max_len:
                score += 0.3
            elif len(value) < min_len or len(value) > max_len * 2:
                score -= 0.5

        # Bonus pour contexte
        context_start = max(0, position - 100)
        context_end = min(len(text), position + len(value) + 100)
        context = text[context_start:context_end].lower()

        context_keywords = {
            "nom_banque": ["banque", "btk", "tuniso"],
            "numero_rc": ["registre", "commerce"],
            "date_edition": ["edité", "date"],
            "montant_principal": ["somme", "principal"],
            "duree": ["durée", "mois"],
            "nom_client": ["client", "emprunteuse"]
        }

        if field in context_keywords:
            keyword_count = sum(1 for kw in context_keywords[field] if kw in context)
            score += keyword_count * 0.2

        return max(0, score)

    def clean_field_value(self, field: str, value: str) -> str:
        """Nettoie la valeur d'un champ"""
        clean_value = value.strip()
        clean_value = re.sub(r'\s+', ' ', clean_value)

        if field == "nom_banque":
            clean_value = re.sub(r'^(?:La\s+)?', '', clean_value, flags=re.IGNORECASE)
        elif field in ["adresse", "adresse_client"]:
            clean_value = re.sub(r'^(?:à\s+)?', '', clean_value, flags=re.IGNORECASE)
        elif field == "forme_juridique":
            clean_value = clean_value.lower()
        elif field in ["taux_interet", "commission_gestion"]:
            if not clean_value.endswith('%') and re.search(r'\d', clean_value):
                clean_value += '%'

        return clean_value

    def call_qwen_optimized(self, text: str) -> Dict:
        """Appel Qwen optimisé avec prompt ultra-spécialisé"""
        if not self.qwen_model or not self.qwen_tokenizer:
            logger.warning("⚠️ Modèle Qwen non disponible")
            return {}

        logger.info("🧠 Analyse Qwen optimisée...")

        try:
            prompt = f"""Tu es un expert en contrats BTK. Extrait PRÉCISÉMENT ces informations au format JSON :

{{
  "informations_banque": {{
    "nom_banque": "BANQUE TUNISO-KOWEITIENNE exactement",
    "capital_social": "montant en dinars",
    "numero_rc": "numéro R.C.",
    "adresse": "adresse complète",
    "forme_juridique": "société anonyme",
    "telephone": "numéro téléphone"
  }},
  "informations_contrat": {{
    "type_contrat": "PRET ou crédit",
    "numero_contrat": "numéro/référence",
    "date_edition": "JJ/MM/AAAA",
    "montant_principal": "somme principale",
    "duree": "X mois",
    "taux_interet": "taux %",
    "commission_gestion": "commission %",
    "garanties": "garanties"
  }},
  "informations_client": {{
    "code_client": "Code Client",
    "nom_client": "nom exact",
    "adresse_client": "adresse client",
    "secteur_activite": "secteur",
    "numero_rc_client": "R.C. client"
  }}
}}

RÈGLES :
- Extraire EXACTEMENT du texte
- Inclure valeurs masquées (xxx, XXXX)
- Format JSON strict
- Vide si non trouvé

TEXTE :
{text[:8000]}

JSON :"""

            # Tokenisation avec attention mask
            inputs = self.qwen_tokenizer(
                prompt,
                return_tensors="pt",
                max_length=4096,
                truncation=True,
                padding=True
            )

            # Génération optimisée
            with torch.no_grad():
                outputs = self.qwen_model.generate(
                    inputs.input_ids,
                    attention_mask=inputs.attention_mask,
                    max_new_tokens=1000,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.qwen_tokenizer.pad_token_id,
                    eos_token_id=self.qwen_tokenizer.eos_token_id
                )

            # Décodage
            response = self.qwen_tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )

            # Extraction JSON
            json_match = re.search(r'\{[\s\S]*\}', response)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    logger.info("✅ Analyse Qwen réussie")
                    return result
                except json.JSONDecodeError:
                    logger.error("❌ Erreur JSON Qwen")

        except Exception as e:
            logger.error(f"❌ Erreur Qwen: {str(e)}")

        return {}

    def detect_masked_fields_final(self, words: List[Dict]) -> List[Dict]:
        """Détection finale optimisée des champs masqués"""
        masked_fields = []
        if not words:
            return masked_fields

        try:
            # Tri et groupement par ligne
            sorted_words = sorted(words, key=lambda w: (w['page'], w['y'], w['x']))
            lines = self.group_words_by_line(sorted_words)

            # Recherche contextuelle des champs masqués
            for i, line in enumerate(lines):
                line_text = " ".join(w["text"] for w in line)

                # Recherche des labels de champs
                for section, fields in self.required_fields.items():
                    for field, field_info in fields.items():
                        for pattern in field_info["patterns"]:
                            if re.search(rf'\b{re.escape(pattern)}\b', line_text, re.IGNORECASE):
                                # Recherche des masques dans cette ligne et les suivantes
                                masked_in_context = self.find_masked_in_context(
                                    lines, i, field, field_info["priority"]
                                )
                                masked_fields.extend(masked_in_context)

        except Exception as e:
            logger.error(f"❌ Erreur détection masqués: {str(e)}")

        return masked_fields

    def group_words_by_line(self, words: List[Dict]) -> List[List[Dict]]:
        """Groupe les mots par ligne avec tolérance adaptative"""
        if not words:
            return []

        lines = []
        current_line = []
        current_y = words[0]['y']

        for word in words:
            tolerance = max(word['height'] * 0.4, 15)

            if abs(word['y'] - current_y) <= tolerance:
                current_line.append(word)
            else:
                if current_line:
                    current_line.sort(key=lambda w: w['x'])
                    lines.append(current_line)
                current_line = [word]
                current_y = word['y']

        if current_line:
            current_line.sort(key=lambda w: w['x'])
            lines.append(current_line)

        return lines

    def find_masked_in_context(self, lines: List[List[Dict]], start_line: int, field: str, priority: int) -> List[Dict]:
        """Trouve les champs masqués dans le contexte"""
        masked_fields = []

        # Recherche dans la ligne courante et les 2 suivantes
        for i in range(start_line, min(start_line + 3, len(lines))):
            for word in lines[i]:
                if self.is_masked_final(word["text"]):
                    confidence = self.calculate_mask_confidence_final(word["text"], priority)

                    masked_fields.append({
                        "champ": field,
                        "masque": word["text"],
                        "position": {
                            "page": word["page"],
                            "x": word["x"],
                            "y": word["y"],
                            "width": word["width"],
                            "height": word["height"]
                        },
                        "confiance": confidence,
                        "priorite": priority
                    })

        return masked_fields

    def is_masked_final(self, text: str) -> bool:
        """Détection finale des champs masqués"""
        if len(text) < 2:
            return False

        # Patterns de masquage étendus
        patterns = [
            r'^X{2,}$', r'^\*{2,}$', r'^•{2,}$', r'^_{3,}$', r'^#{2,}$',
            r'^[X*•_#\s]{3,}$', r'^xxx+$', r'^XXX+$', r'^\[.*\]$',
            r'^<.*>$', r'^\{.*\}$'
        ]

        for pattern in patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True

        # Densité de caractères de masquage
        mask_chars = sum(1 for c in text if c in 'X*•_#')
        if len(text) > 0 and mask_chars / len(text) > 0.7:
            return True

        return False

    def calculate_mask_confidence_final(self, text: str, priority: int) -> float:
        """Calcule la confiance finale pour un masque"""
        if not text:
            return 0.0

        # Facteurs de base
        mask_chars = sum(1 for c in text if c in 'X*•_#')
        length_factor = min(len(text) / 10, 1.0)
        density_factor = mask_chars / len(text) if len(text) > 0 else 0

        # Bonus pour patterns typiques
        pattern_bonus = 0.3 if re.match(r'^[X*•_#]{3,}$', text) else 0

        # Bonus pour priorité du champ
        priority_bonus = (4 - priority) * 0.1  # Plus la priorité est haute, plus le bonus

        confidence = (density_factor * 0.5 + length_factor * 0.2 + pattern_bonus + priority_bonus)
        return min(confidence, 1.0)

    def process_contract_final(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement final optimisé d'un contrat"""
        logger.info(f"🚀 TRAITEMENT FINAL OPTIMISÉ: {pdf_name}")

        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "champs_masques": [],
            "scores_qualite": {}
        }

        try:
            # 1. Extraction OCR optimisée
            logger.info("📄 Phase 1: Extraction OCR optimisée...")
            ocr_text, word_positions = self.extract_text_optimized(pdf_path, pdf_name)

            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")

            logger.info(f"📝 Longueur du texte: {len(ocr_text)} caractères")

            # 2. Extraction ultra-précise
            logger.info("🔍 Phase 2: Extraction ultra-précise...")
            regex_results = self.extract_with_ultra_precision(ocr_text)

            # 3. Analyse Qwen optimisée
            qwen_results = {}
            if self.qwen_model:
                logger.info("🧠 Phase 3: Analyse Qwen optimisée...")
                qwen_results = self.call_qwen_optimized(ocr_text)

            # 4. Fusion intelligente
            logger.info("🔄 Phase 4: Fusion intelligente...")
            final_results = self.merge_results_intelligent(regex_results, qwen_results)

            # 5. Détection finale des masqués
            logger.info("🎭 Phase 5: Détection finale des masqués...")
            masked_fields = self.detect_masked_fields_final(word_positions)

            # 6. Calcul des scores finaux
            quality_scores = self.calculate_final_scores(final_results, masked_fields)

            # 7. Assemblage final
            result.update(final_results)
            result["champs_masques"] = masked_fields
            result["scores_qualite"] = quality_scores

            # 8. Métadonnées
            result['metadata'] = {
                'fichier_source': f"{pdf_name}.pdf",
                'extraction_timestamp': datetime.now().isoformat(),
                'methode_extraction': 'Final Optimized BTK Extractor',
                'ocr_text_length': len(ocr_text),
                'masked_fields_count': len(masked_fields),
                'qwen_available': self.qwen_model is not None,
                'total_words_detected': len(word_positions),
                'quality_score_global': quality_scores.get('score_global', 0)
            }

            # 9. Affichage du résumé
            self.display_final_summary(result)

            logger.info(f"✅ TRAITEMENT FINAL TERMINÉ avec succès!")
            return result

        except Exception as e:
            logger.error(f"❌ Erreur traitement final {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf",
                "informations_banque": {},
                "informations_contrat": {},
                "informations_client": {},
                "champs_masques": [],
                "scores_qualite": {"score_global": 0}
            }
