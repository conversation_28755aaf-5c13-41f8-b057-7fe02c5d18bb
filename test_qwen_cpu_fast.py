#!/usr/bin/env python3
"""
Test rapide de Qwen 2.5 optimisé pour CPU
"""

import os
import json
import time
from detection_qwen_cpu_optimized import QwenCPUOptimizedExtractor

def test_qwen_cpu_performance():
    """Test de performance Qwen 2.5 sur CPU"""
    
    print("🚀 === TEST QWEN 2.5 OPTIMISÉ CPU ===")
    print("="*50)
    
    # Mesure du temps d'initialisation
    start_init = time.time()
    extractor = QwenCPUOptimizedExtractor()
    init_time = time.time() - start_init
    
    print(f"⏱️  Initialisation: {init_time:.2f}s")
    
    # Vérifier si Qwen est chargé
    qwen_status = "✅ Chargé" if extractor.qwen_model else "❌ Non disponible"
    print(f"🧠 Qwen 2.5-1.5B: {qwen_status}")
    
    # Test sur fichier
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"\n🧪 Test sur: {test_file}")
    print("-" * 50)
    
    # Traitement avec mesure de temps
    start_process = time.time()
    result = extractor.process_contract_cpu_optimized(pdf_path, pdf_name)
    process_time = time.time() - start_process
    
    print(f"\n⏱️  Temps de traitement: {process_time:.2f}s")
    
    # Analyse des résultats
    print(f"\n📊 RÉSULTATS QWEN 2.5 CPU:")
    print("="*50)
    
    sections = ["informations_banque", "informations_contrat", "informations_client", "informations_signatures"]
    total_found = 0
    total_possible = 0
    
    for section in sections:
        section_name = section.replace("informations_", "").upper()
        section_data = result.get(section, {})
        
        found = sum(1 for v in section_data.values() if v and str(v).strip())
        possible = len(extractor.required_fields.get(section, []))
        
        total_found += found
        total_possible += possible
        
        percentage = (found / possible * 100) if possible > 0 else 0
        print(f"\n🏷️  {section_name}: {found}/{possible} ({percentage:.1f}%)")
        
        # Afficher les champs trouvés
        if section_data:
            for field, value in section_data.items():
                if value and str(value).strip():
                    display_value = str(value)[:60] + "..." if len(str(value)) > 60 else str(value)
                    print(f"   ✅ {field}: {display_value}")
    
    # Score global
    global_score = (total_found / total_possible * 100) if total_possible > 0 else 0
    scores = result.get("scores_qualite", {})
    
    print(f"\n⭐ SCORES DE QUALITÉ:")
    print(f"   🎯 Score global: {global_score:.1f}%")
    print(f"   🏦 Banque: {scores.get('completude_banque', 0):.1f}%")
    print(f"   📄 Contrat: {scores.get('completude_contrat', 0):.1f}%")
    print(f"   👤 Client: {scores.get('completude_client', 0):.1f}%")
    print(f"   ✍️  Signatures: {scores.get('completude_signatures', 0):.1f}%")
    
    # Comparaison avec versions précédentes
    print(f"\n📈 COMPARAISON PERFORMANCES:")
    comparisons = {
        "Version originale": {"score": 22, "temps": "~60s"},
        "Version améliorée": {"score": 37, "temps": "~90s"},
        "Qwen 2.5 CPU": {"score": global_score, "temps": f"{process_time:.1f}s"}
    }
    
    for version, data in comparisons.items():
        if version == "Qwen 2.5 CPU":
            status = "🎉" if data["score"] > 40 else "📊"
            print(f"   {status} {version}: {data['score']:.1f}% en {data['temps']}")
        else:
            print(f"   📊 {version}: {data['score']}% en {data['temps']}")
    
    # Analyse des améliorations
    print(f"\n💡 AVANTAGES QWEN 2.5 CPU:")
    advantages = [
        f"🧠 Modèle plus intelligent (1.5B paramètres)",
        f"⚡ Optimisé pour CPU (float32, cache, threads)",
        f"🎯 Meilleure compréhension contextuelle",
        f"📝 Extraction JSON plus fiable",
        f"🔧 Prompt optimisé pour contrats BTK",
        f"⏱️  Temps de traitement raisonnable"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")
    
    # Recommandations
    print(f"\n🎯 RECOMMANDATIONS:")
    if global_score > 45:
        print("   ✅ Excellente performance - Système prêt pour production")
    elif global_score > 35:
        print("   ⚠️  Bonne performance - Quelques ajustements possibles")
    else:
        print("   🔧 Performance à améliorer - Vérifier configuration")
    
    if process_time < 120:
        print("   ⚡ Temps de traitement acceptable pour CPU")
    else:
        print("   ⏱️  Temps de traitement élevé - Considérer optimisations")
    
    # Sauvegarde avec timestamp
    timestamp = int(time.time())
    output_path = f"results/{pdf_name}_QWEN25_CPU_{timestamp}.json"
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Résultat sauvegardé: {output_path}")
    
    return result, global_score, process_time

def analyze_cpu_optimization():
    """Analyse des optimisations CPU"""
    
    print(f"\n🔧 OPTIMISATIONS CPU APPLIQUÉES:")
    print("="*50)
    
    optimizations = {
        "Modèle": "Qwen2.5-1.5B-Instruct (équilibre taille/performance)",
        "Précision": "Float32 (optimal pour CPU)",
        "Threads": "4 threads CPU configurés",
        "Cache": "Activé pour accélérer génération",
        "Tokenizer": "Version rapide (use_fast=True)",
        "Mémoire": "low_cpu_mem_usage=True",
        "Génération": "Paramètres optimisés (temperature=0.1, top_p=0.9)",
        "OCR": "DPI réduit (200) pour vitesse",
        "Prompt": "Format optimisé pour Qwen 2.5"
    }
    
    for aspect, detail in optimizations.items():
        print(f"   🔧 {aspect}: {detail}")
    
    print(f"\n⚡ AVANTAGES CPU vs GPU:")
    cpu_advantages = [
        "✅ Pas besoin de GPU coûteux",
        "✅ Consommation électrique réduite", 
        "✅ Déploiement plus simple",
        "✅ Coût d'infrastructure moindre",
        "✅ Disponibilité universelle",
        "✅ Stabilité et fiabilité"
    ]
    
    for advantage in cpu_advantages:
        print(f"   {advantage}")

if __name__ == "__main__":
    try:
        result, score, time_taken = test_qwen_cpu_performance()
        analyze_cpu_optimization()
        
        print(f"\n🎉 TEST QWEN 2.5 CPU TERMINÉ!")
        print(f"📊 Score final: {score:.1f}%")
        print(f"⏱️  Temps total: {time_taken:.1f}s")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()
