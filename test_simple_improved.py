#!/usr/bin/env python3
"""
Test simple avec patterns améliorés (sans Qwen pour test rapide)
"""

import os
import json
import time
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

class SimpleImprovedExtractor:
    """Extracteur simple avec patterns améliorés"""
    
    def __init__(self):
        self.ocr_config = r'--oem 3 --psm 6 -l fra+eng+ara'
        
        # Champs obligatoires
        self.required_fields = {
            "informations_banque": [
                "nom_banque", "capital_social", "numero_rc", "adresse", 
                "forme_juridique", "telephone"
            ],
            "informations_contrat": [
                "type_contrat", "numero_contrat", "date_edition", "date_signature",
                "montant_principal", "duree", "taux_interet", "taux_semestriel",
                "commission_gestion", "garanties"
            ],
            "informations_client": [
                "code_client", "nom_client", "numero_cin", "adresse_client",
                "secteur_activite", "numero_rc_client"
            ]
        }

    def enhance_image(self, image):
        """Amélioration d'image pour OCR"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage
        denoised = cv2.medianBlur(img_array, 3)
        
        # Amélioration contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return Image.fromarray(binary)

    def extract_text(self, pdf_path, pdf_name):
        """Extraction de texte OCR"""
        print(f"📄 Extraction OCR pour {pdf_name}")
        
        try:
            pages = convert_from_path(pdf_path, dpi=200, fmt='png')
        except Exception as e:
            print(f"❌ Erreur conversion PDF: {str(e)}")
            return ""
        
        all_text = ""
        
        for i, page in enumerate(pages):
            print(f"🔍 Page {i+1}...")
            
            try:
                enhanced_page = self.enhance_image(page)
                text = pytesseract.image_to_string(enhanced_page, config=self.ocr_config)
                all_text += f"\n--- Page {i+1} ---\n{text}"
                print(f"✅ Page {i+1} traitée")
            except Exception as e:
                print(f"❌ Erreur page {i+1}: {str(e)}")
        
        return all_text

    def extract_with_patterns(self, text):
        """Extraction avec patterns améliorés"""
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {}
        }
        
        clean_text = re.sub(r'\s+', ' ', text)
        
        # Patterns optimisés
        patterns = {
            "informations_banque": {
                "nom_banque": [
                    r"(?:La\s+)?BANQUE\s+TUNISO[\-\s]*KOWEITIENNE",
                    r"B\.T\.K\.?",
                    r"(?<![\w])BTK(?![\w])"
                ],
                "capital_social": [
                    r"capital\s+de\s+([\d\s,\.]+)\s*dinars?",
                    r"au\s+capital\s+de\s+([\d\s,\.]+)"
                ],
                "numero_rc": [
                    r"R\.?C\.?\s*[=:]\s*([A-Z]?\d+)",
                    r"registre\s+(?:de\s+)?commerce\s*[=:]\s*([A-Z]?\d+)"
                ],
                "adresse": [
                    r"siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)",
                    r"(\d+\s*bis[,\s]+Avenue\s+[^,\n\.]+)"
                ],
                "forme_juridique": [
                    r"(société\s+anonyme)",
                    r"(S\.A\.?)"
                ],
                "telephone": [
                    r"(?:Tél|Tel|Téléphone)\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})"
                ]
            },
            "informations_contrat": {
                "type_contrat": [
                    r"CONTRAT\s+DE\s+(PRET|PRÊT)",
                    r"contrat\s+de\s+(crédit|prêt)"
                ],
                "numero_contrat": [
                    r"Numéro\s+(?:de\s+)?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)",
                    r"Référence\s*[=:]\s*([A-Z0-9\-\/]+)"
                ],
                "date_edition": [
                    r"Edité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"Date\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "date_signature": [
                    r"signé\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"fait\s+le\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "montant_principal": [
                    r"somme\s+principale\s+(?:de\s+)?([\d\s,\.]+)",
                    r"montant\s+(?:du\s+)?(?:crédit|prêt)\s*[=:]\s*([\d\s,\.]+)"
                ],
                "duree": [
                    r"durée\s+de\s+(\d+\s+mois)",
                    r"pour\s+une\s+durée\s+de\s+(\d+\s+mois)"
                ],
                "taux_interet": [
                    r"taux\s+d['\']intérêt\s*[=:]\s*([\d,\.]+\s*%?)",
                    r"intérêts\s+au\s+taux\s+(?:de\s+)?([\d,\.]+\s*%)"
                ],
                "taux_semestriel": [
                    r"taux\s+semestriel\s+(?:du\s+marché\s+monétaire\s+)?(?:majoré\s+de\s+)?([\d,\.]+\s*%?)"
                ],
                "commission_gestion": [
                    r"commission\s+de\s+gestion\s+de\s+([\d,\.]+\s*%)"
                ],
                "garanties": [
                    r"garanties?\s*[=:]\s*([^\.]+)",
                    r"hypothèque\s+sur\s+([^\.]+)"
                ]
            },
            "informations_client": {
                "code_client": [
                    r"Code\s+Client\s*[=:]\s*([A-Z0-9X]{4,})"
                ],
                "nom_client": [
                    r"Et\s+([A-Z][A-Z\s&\-\.]+),?\s+dont\s+le\s+siège",
                    r"Emprunteuse?\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)"
                ],
                "numero_cin": [
                    r"CIN\s*[=:]\s*(\d{8})",
                    r"C\.I\.N\.?\s*[=:]\s*(\d{8})"
                ],
                "adresse_client": [
                    r"dont\s+le\s+siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)"
                ],
                "numero_rc_client": [
                    r"immatriculé(?:e)?\s+au\s+(?:Centre\s+National\s+du\s+)?Registre\s+(?:des\s+Entreprises\s+)?sous\s+(?:l['\']identifiant\s+unique\s+)?n°\s*([A-Z0-9]+)"
                ],
                "secteur_activite": [
                    r"Secteur\s+d['\']activité\s*[=:]\s*([^,\n\.]+)"
                ]
            }
        }
        
        # Application des patterns
        for section, section_patterns in patterns.items():
            for field, field_patterns in section_patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, clean_text, re.IGNORECASE)
                    if match:
                        value = match.group(1).strip() if match.groups() else match.group(0).strip()
                        if value and not result[section].get(field):
                            result[section][field] = value
                            break
        
        return result

    def calculate_scores(self, results):
        """Calcul des scores"""
        scores = {}
        total_found = 0
        total_possible = 0
        
        for section, required in self.required_fields.items():
            found = len([f for f in required if results.get(section, {}).get(f)])
            scores[f"completude_{section.split('_')[1]}"] = (found / len(required)) * 100
            total_found += found
            total_possible += len(required)
        
        scores["score_global"] = (total_found / total_possible) * 100 if total_possible > 0 else 0
        
        return scores

    def process_contract(self, pdf_path, pdf_name):
        """Traitement complet d'un contrat"""
        print(f"🚀 TRAITEMENT: {pdf_name}")
        
        try:
            # Extraction OCR
            ocr_text = self.extract_text(pdf_path, pdf_name)
            
            if not ocr_text.strip():
                raise Exception("Aucun texte extrait")
            
            print(f"📝 Texte extrait: {len(ocr_text)} caractères")
            
            # Extraction avec patterns
            results = self.extract_with_patterns(ocr_text)
            
            # Calcul des scores
            scores = self.calculate_scores(results)
            
            # Assemblage final
            final_result = {
                **results,
                "scores_qualite": scores,
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Simple Improved Patterns",
                    "ocr_text_length": len(ocr_text),
                    "quality_score_global": scores.get("score_global", 0)
                }
            }
            
            print(f"✅ TRAITEMENT TERMINÉ!")
            return final_result
            
        except Exception as e:
            print(f"❌ Erreur: {str(e)}")
            return {"erreur": str(e)}

def test_simple_improved():
    """Test de la version simple améliorée"""
    
    print("🚀 === TEST PATTERNS AMÉLIORÉS SIMPLES ===")
    print("="*50)
    
    # Initialisation
    start_time = time.time()
    extractor = SimpleImprovedExtractor()
    init_time = time.time() - start_time
    
    print(f"⏱️  Initialisation: {init_time:.2f}s")
    
    # Test
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"\n🧪 Test sur: {test_file}")
    print("-" * 50)
    
    # Traitement
    start_process = time.time()
    result = extractor.process_contract(pdf_path, pdf_name)
    process_time = time.time() - start_process
    
    print(f"\n⏱️  Temps total: {process_time:.2f}s")
    
    # Résultats
    if "erreur" not in result:
        print(f"\n📊 RÉSULTATS:")
        print("="*50)
        
        total_found = 0
        total_possible = 0
        
        for section in ["informations_banque", "informations_contrat", "informations_client"]:
            section_name = section.replace("informations_", "").upper()
            section_data = result.get(section, {})
            
            found = sum(1 for v in section_data.values() if v and str(v).strip())
            possible = len(extractor.required_fields.get(section, []))
            
            total_found += found
            total_possible += possible
            
            print(f"\n🏷️  {section_name}: {found}/{possible} ({found/possible*100 if possible > 0 else 0:.1f}%)")
            
            for field, value in section_data.items():
                if value and str(value).strip():
                    display_value = str(value)[:60] + "..." if len(str(value)) > 60 else str(value)
                    print(f"   ✅ {field}: {display_value}")
        
        # Score global
        global_score = (total_found / total_possible * 100) if total_possible > 0 else 0
        scores = result.get("scores_qualite", {})
        
        print(f"\n⭐ SCORES:")
        print(f"   🎯 Global: {global_score:.1f}%")
        print(f"   🏦 Banque: {scores.get('completude_banque', 0):.1f}%")
        print(f"   📄 Contrat: {scores.get('completude_contrat', 0):.1f}%")
        print(f"   👤 Client: {scores.get('completude_client', 0):.1f}%")
        
        # Sauvegarde
        output_path = f"results/{pdf_name}_SIMPLE_IMPROVED.json"
        os.makedirs("results", exist_ok=True)
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Sauvegardé: {output_path}")
        
        return global_score
    else:
        print(f"❌ Erreur: {result.get('erreur')}")
        return 0

if __name__ == "__main__":
    try:
        score = test_simple_improved()
        print(f"\n🎉 TEST TERMINÉ!")
        print(f"📊 Score final: {score:.1f}%")
        print("="*50)
        
    except Exception as e:
        print(f"\n❌ ERREUR: {str(e)}")
        import traceback
        traceback.print_exc()
