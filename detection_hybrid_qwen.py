#!/usr/bin/env python3
"""
EXTRACTEUR HYBRIDE COMPLET : PATTERNS REGEX + QWEN 2.5
Combine la précision des patterns avec l'intelligence de Qwen 2.5
"""

import os
import json
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Imports pour Qwen 2.5
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False
    print("⚠️ Transformers non disponible - Qwen désactivé")

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

class HybridQwenExtractor:
    """Extracteur hybride combinant patterns regex + Qwen 2.5"""
    
    def __init__(self):
        self.ocr_config = r'--oem 3 --psm 6 -l fra+eng+ara'
        
        # Configuration des champs (reprise du système complet)
        self.all_possible_fields = {
            "informations_banque": {
                "nom_banque": {"required": True, "type": "text"},
                "capital_social": {"required": True, "type": "amount"},
                "numero_rc": {"required": True, "type": "identifier"},
                "adresse": {"required": True, "type": "address"},
                "forme_juridique": {"required": True, "type": "text"},
                "telephone": {"required": False, "type": "phone"},
                "fax": {"required": False, "type": "phone"},
                "email": {"required": False, "type": "email"}
            },
            "informations_contrat": {
                "type_contrat": {"required": True, "type": "text"},
                "numero_contrat": {"required": True, "type": "identifier"},
                "date_edition": {"required": True, "type": "date"},
                "date_signature": {"required": True, "type": "date"},
                "montant_principal": {"required": True, "type": "amount"},
                "devise": {"required": False, "type": "text"},
                "duree": {"required": True, "type": "duration"},
                "taux_interet": {"required": True, "type": "percentage"},
                "taux_semestriel": {"required": False, "type": "percentage"},
                "taux_effectif_global": {"required": False, "type": "percentage"},
                "commission_gestion": {"required": True, "type": "percentage"},
                "commission_engagement": {"required": False, "type": "percentage"},
                "garanties": {"required": True, "type": "text"}
            },
            "informations_client": {
                "code_client": {"required": True, "type": "identifier"},
                "nom_client": {"required": True, "type": "text"},
                "numero_cin": {"required": True, "type": "cin"},
                "adresse_client": {"required": True, "type": "address"},
                "secteur_activite": {"required": True, "type": "text"},
                "numero_rc_client": {"required": True, "type": "identifier"},
                "profession": {"required": False, "type": "text"},
                "numero_compte": {"required": False, "type": "account"}
            },
            "informations_signatures": {
                "date_signature": {"required": True, "type": "date"},
                "lieu_signature": {"required": True, "type": "text"},
                "representant_banque": {"required": True, "type": "text"}
            }
        }
        
        # Initialisation de Qwen 2.5
        self.qwen_model = None
        self.qwen_tokenizer = None
        self.init_qwen_model()

    def init_qwen_model(self):
        """Initialise Qwen 2.5 optimisé pour CPU"""
        if not QWEN_AVAILABLE:
            print("⚠️ Qwen non disponible - utilisation patterns seuls")
            return
            
        try:
            print("🧠 Chargement Qwen 2.5 pour extraction hybride...")
            
            # Modèle optimal pour CPU
            model_name = "Qwen/Qwen2.5-1.5B-Instruct"
            
            # Tokenizer optimisé
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                model_name, 
                trust_remote_code=True,
                use_fast=True
            )
            
            # Modèle optimisé CPU
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float32,
                device_map=None,
                trust_remote_code=True,
                low_cpu_mem_usage=True,
                use_cache=True
            )
            
            self.qwen_model = self.qwen_model.to("cpu")
            self.qwen_model.eval()
            
            # Optimisations PyTorch CPU
            torch.set_num_threads(4)
            
            print("✅ Qwen 2.5 chargé et optimisé pour extraction hybride!")
            
        except Exception as e:
            print(f"❌ Erreur chargement Qwen: {str(e)}")
            self.qwen_model = None
            self.qwen_tokenizer = None

    def enhance_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """Amélioration d'image pour OCR optimal"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage adaptatif
        denoised = cv2.fastNlMeansDenoising(img_array, None, 10, 7, 21)
        
        # Amélioration contraste CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        return Image.fromarray(binary)

    def extract_text_optimized(self, pdf_path: str, pdf_name: str) -> str:
        """Extraction de texte optimisée pour l'analyse hybride"""
        print(f"📄 Extraction OCR optimisée pour analyse hybride: {pdf_name}")
        
        # Créer dossiers
        for folder in ['images', 'ocr_texts']:
            os.makedirs(folder, exist_ok=True)
        
        try:
            pages = convert_from_path(pdf_path, dpi=300, fmt='png')
        except Exception as e:
            print(f"❌ Erreur conversion PDF: {str(e)}")
            return ""
        
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            print(f"🔍 Page {page_num}...")
            
            try:
                # Amélioration d'image
                enhanced_page = self.enhance_image_for_ocr(page)
                
                # Sauvegarde image
                img_path = f"images/{pdf_name}_hybrid_page_{page_num}.png"
                enhanced_page.save(img_path, "PNG")
                
                # OCR texte
                text = pytesseract.image_to_string(enhanced_page, config=self.ocr_config)
                all_text += f"\n--- Page {page_num} ---\n{text}"
                
                print(f"✅ Page {page_num} traitée")
            except Exception as e:
                print(f"❌ Erreur page {page_num}: {str(e)}")
        
        # Sauvegarde texte
        text_path = f"ocr_texts/{pdf_name}_hybrid.txt"
        try:
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(all_text)
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {str(e)}")
        
        return all_text

    def extract_with_advanced_patterns(self, text: str) -> Dict:
        """Extraction avec patterns regex ultra-avancés"""
        print("🔍 Phase REGEX: Extraction avec patterns avancés...")
        
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_signatures": {}
        }
        
        # Nettoyage du texte
        clean_text = re.sub(r'\s+', ' ', text)
        
        # Patterns ultra-optimisés (repris du système complet)
        patterns = {
            "informations_banque": {
                "nom_banque": [
                    r"(?:La\s+)?BANQUE\s+TUNISO[\-\s]*KOWEITIENNE",
                    r"B\.T\.K\.?",
                    r"(?<![\w])BTK(?![\w])"
                ],
                "capital_social": [
                    r"capital\s+de\s+([\d\s,\.]+)\s*dinars?",
                    r"au\s+capital\s+de\s+([\d\s,\.]+)"
                ],
                "numero_rc": [
                    r"R\.?C\.?\s*[=:]\s*([A-Z]?\d+)",
                    r"registre\s+(?:de\s+)?commerce\s*[=:]\s*([A-Z]?\d+)"
                ],
                "adresse": [
                    r"siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)",
                    r"(\d+\s*bis[,\s]+Avenue\s+[^,\n\.]+)"
                ],
                "forme_juridique": [
                    r"(société\s+anonyme)",
                    r"(S\.A\.?)"
                ],
                "telephone": [
                    r"(?:Tél|Tel|Téléphone)\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})"
                ]
            },
            "informations_contrat": {
                "type_contrat": [
                    r"CONTRAT\s+DE\s+(PRET|PRÊT)",
                    r"contrat\s+de\s+(crédit|prêt)"
                ],
                "numero_contrat": [
                    r"Numéro\s+(?:de\s+)?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)",
                    r"Référence\s*[=:]\s*([A-Z0-9\-\/]+)"
                ],
                "date_edition": [
                    r"Edité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})",
                    r"Date\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})"
                ],
                "montant_principal": [
                    r"somme\s+principale\s+(?:de\s+)?([\d\s,\.]+)",
                    r"montant\s+(?:du\s+)?(?:crédit|prêt)\s*[=:]\s*([\d\s,\.]+)"
                ],
                "duree": [
                    r"durée\s+de\s+(\d+\s+mois)",
                    r"pour\s+une\s+durée\s+de\s+(\d+\s+mois)"
                ],
                "taux_interet": [
                    r"taux\s+d['\']intérêt\s*[=:]\s*([\d,\.]+\s*%?)",
                    r"intérêts\s+au\s+taux\s+(?:de\s+)?([\d,\.]+\s*%)"
                ],
                "commission_gestion": [
                    r"commission\s+de\s+gestion\s+de\s+([\d,\.]+\s*%)"
                ]
            },
            "informations_client": {
                "code_client": [
                    r"Code\s+Client\s*[=:]\s*([A-Z0-9X]{4,})"
                ],
                "nom_client": [
                    r"Et\s+([A-Z][A-Z\s&\-\.]+),?\s+dont\s+le\s+siège",
                    r"Emprunteuse?\s*[=:]\s*([A-Z][A-Z\s&\-\.]+)"
                ],
                "numero_cin": [
                    r"CIN\s*[=:]\s*(\d{8})",
                    r"C\.I\.N\.?\s*[=:]\s*(\d{8})"
                ],
                "adresse_client": [
                    r"dont\s+le\s+siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)"
                ]
            }
        }
        
        # Application des patterns
        for section, section_patterns in patterns.items():
            for field, field_patterns in section_patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, clean_text, re.IGNORECASE)
                    if match:
                        value = match.group(1).strip() if match.groups() else match.group(0).strip()
                        if value and not result[section].get(field):
                            result[section][field] = value
                            break
        
        return result

    def extract_with_qwen_intelligence(self, text: str) -> Dict:
        """Extraction avec intelligence Qwen 2.5"""
        if not self.qwen_model or not self.qwen_tokenizer:
            print("⚠️ Qwen non disponible - utilisation patterns seuls")
            return {}

        print("🧠 Phase QWEN: Analyse intelligente avec Qwen 2.5...")

        try:
            # Prompt spécialisé pour Qwen 2.5
            prompt = f"""<|im_start|>system
Tu es un expert en analyse de contrats bancaires de la Banque Tuniso-Koweitienne (BTK).
Tu dois extraire avec une précision maximale toutes les informations contractuelles au format JSON strict.

EXPERTISE REQUISE :
- Analyse contextuelle avancée des documents juridiques tunisiens
- Reconnaissance de patterns financiers et bancaires BTK
- Extraction d'informations même partiellement masquées (xxx, XXXX)
- Compréhension des termes juridiques français et arabes
- Détection des champs obligatoires vs optionnels
<|im_end|>

<|im_start|>user
Analyse ce contrat BTK et extrait TOUTES les informations au format JSON strict :

STRUCTURE JSON OBLIGATOIRE :
{{
  "informations_banque": {{
    "nom_banque": "",
    "capital_social": "",
    "numero_rc": "",
    "adresse": "",
    "forme_juridique": "",
    "telephone": "",
    "fax": "",
    "email": ""
  }},
  "informations_contrat": {{
    "type_contrat": "",
    "numero_contrat": "",
    "date_edition": "",
    "date_signature": "",
    "montant_principal": "",
    "devise": "",
    "duree": "",
    "taux_interet": "",
    "taux_semestriel": "",
    "taux_effectif_global": "",
    "commission_gestion": "",
    "commission_engagement": "",
    "garanties": ""
  }},
  "informations_client": {{
    "code_client": "",
    "nom_client": "",
    "numero_cin": "",
    "adresse_client": "",
    "secteur_activite": "",
    "numero_rc_client": "",
    "profession": "",
    "numero_compte": ""
  }},
  "informations_signatures": {{
    "date_signature": "",
    "lieu_signature": "",
    "representant_banque": ""
  }}
}}

RÈGLES D'EXTRACTION STRICTES :
1. BANQUE : "BANQUE TUNISO-KOWEITIENNE" ou "B.T.K", capital en dinars, R.C., siège social Avenue Mohamed V
2. CONTRAT : "CONTRAT DE PRET", dates JJ/MM/AAAA, montant principal, durée en mois, TOUS les taux %
3. CLIENT : Code Client, nom complet, CIN 8 chiffres exactement, adresse, R.C., secteur
4. SIGNATURES : Date signature, lieu (souvent Tunis), représentant banque
5. MASQUÉS : Conserver xxx, XXXX, etc. comme valeurs exactes
6. DATES : Format JJ/MM/AAAA obligatoire
7. CIN : Exactement 8 chiffres
8. TAUX : Inclure % si présent
9. JSON : Valide et complet uniquement

TEXTE DU CONTRAT BTK :
{text[:10000]}
<|im_end|>

<|im_start|>assistant
```json"""

            # Tokenisation optimisée
            inputs = self.qwen_tokenizer(
                prompt,
                return_tensors="pt",
                max_length=6144,  # Contexte étendu pour Qwen 2.5
                truncation=True,
                padding=False
            )

            # Génération optimisée pour extraction
            with torch.no_grad():
                outputs = self.qwen_model.generate(
                    inputs.input_ids,
                    max_new_tokens=1200,  # Plus de tokens pour réponse complète
                    temperature=0.05,     # Très basse pour précision maximale
                    do_sample=True,
                    top_p=0.85,          # Nucleus sampling optimisé
                    repetition_penalty=1.1,
                    pad_token_id=self.qwen_tokenizer.eos_token_id,
                    eos_token_id=self.qwen_tokenizer.eos_token_id,
                    use_cache=True
                )

            # Décodage
            response = self.qwen_tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:],
                skip_special_tokens=True
            )

            # Extraction JSON avec nettoyage
            json_match = re.search(r'\{[\s\S]*\}', response)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    print("✅ Analyse Qwen 2.5 réussie")
                    return result
                except json.JSONDecodeError as e:
                    print(f"❌ Erreur JSON Qwen: {str(e)}")
                    # Tentative de réparation JSON
                    return self.repair_json_response(json_match.group())

        except Exception as e:
            print(f"❌ Erreur Qwen 2.5: {str(e)}")

        return {}

    def repair_json_response(self, json_str: str) -> Dict:
        """Tentative de réparation d'un JSON malformé"""
        try:
            # Nettoyages courants
            cleaned = json_str.strip()
            cleaned = re.sub(r',\s*}', '}', cleaned)  # Virgules en trop
            cleaned = re.sub(r',\s*]', ']', cleaned)  # Virgules en trop
            cleaned = re.sub(r'}\s*{', '},{', cleaned)  # Objets collés

            return json.loads(cleaned)
        except:
            print("❌ Impossible de réparer le JSON Qwen")
            return {}

    def merge_regex_and_qwen_results(self, regex_result: Dict, qwen_result: Dict) -> Dict:
        """Fusion intelligente des résultats regex et Qwen"""
        print("🔄 Phase FUSION: Combinaison intelligente regex + Qwen...")

        merged = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_signatures": {}
        }

        for section in merged.keys():
            # Commencer avec les résultats regex (base fiable)
            merged[section] = regex_result.get(section, {}).copy()

            # Enrichir avec Qwen (intelligence contextuelle)
            qwen_section = qwen_result.get(section, {})
            for field, qwen_value in qwen_section.items():
                if qwen_value and str(qwen_value).strip():
                    current_value = merged[section].get(field, "")

                    # Logique de fusion intelligente
                    if not current_value:
                        # Pas de valeur regex -> utiliser Qwen
                        merged[section][field] = qwen_value
                    elif self.is_qwen_value_better(current_value, qwen_value, field):
                        # Qwen apporte une amélioration -> utiliser Qwen
                        merged[section][field] = qwen_value
                    # Sinon garder la valeur regex

        return merged

    def is_qwen_value_better(self, regex_value: str, qwen_value: str, field: str) -> bool:
        """Détermine si la valeur Qwen est meilleure que regex"""

        # Si regex a trouvé une valeur masquée et Qwen une vraie valeur
        if self.is_masked_value(regex_value) and not self.is_masked_value(qwen_value):
            return True

        # Si Qwen apporte plus de détails
        if len(str(qwen_value)) > len(str(regex_value)) * 1.5:
            return True

        # Validations spécifiques par type de champ
        field_type = self.get_field_type(field)

        if field_type == "date":
            # Préférer le format date valide
            regex_valid = bool(re.match(r'\d{1,2}\/\d{1,2}\/\d{4}', regex_value))
            qwen_valid = bool(re.match(r'\d{1,2}\/\d{1,2}\/\d{4}', qwen_value))
            if qwen_valid and not regex_valid:
                return True

        elif field_type == "cin":
            # Préférer CIN 8 chiffres exact
            regex_cin = bool(re.match(r'^\d{8}$', regex_value))
            qwen_cin = bool(re.match(r'^\d{8}$', qwen_value))
            if qwen_cin and not regex_cin:
                return True

        elif field_type == "percentage":
            # Préférer avec %
            if '%' in qwen_value and '%' not in regex_value:
                return True

        return False

    def is_masked_value(self, text: str) -> bool:
        """Vérifie si une valeur est masquée"""
        if not text:
            return False

        masked_patterns = [
            r'^X{2,}$', r'^\*{2,}$', r'^•{2,}$', r'^_{3,}$',
            r'^[X*•_#\s]{3,}$', r'^xxx+$', r'^XXX+$'
        ]

        for pattern in masked_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True

        return False

    def get_field_type(self, field: str) -> str:
        """Obtient le type d'un champ"""
        for section, fields in self.all_possible_fields.items():
            if field in fields:
                return fields[field]["type"]
        return "text"

    def calculate_hybrid_scores(self, result: Dict) -> Dict:
        """Calcul des scores pour le système hybride"""
        scores = {
            "score_global": 0,
            "completude_banque": 0,
            "completude_contrat": 0,
            "completude_client": 0,
            "completude_signatures": 0,
            "champs_detectes": 0,
            "champs_obligatoires_detectes": 0,
            "methode_hybride": True
        }

        total_detected = 0
        total_possible = 0
        total_required_detected = 0
        total_required = 0

        # Calcul par section
        for section, fields in self.all_possible_fields.items():
            section_key = f"completude_{section.split('_')[1]}"

            detected = 0
            required_detected = 0
            required_count = 0

            for field, field_info in fields.items():
                is_required = field_info.get("required", False)
                has_value = bool(result.get(section, {}).get(field))

                if has_value:
                    detected += 1
                    total_detected += 1

                    if is_required:
                        required_detected += 1
                        total_required_detected += 1

                if is_required:
                    required_count += 1
                    total_required += 1

                total_possible += 1

            scores[section_key] = (detected / len(fields)) * 100 if fields else 0

        # Scores globaux
        scores["score_global"] = (total_detected / total_possible) * 100 if total_possible > 0 else 0
        scores["champs_detectes"] = total_detected
        scores["champs_obligatoires_detectes"] = (total_required_detected / total_required) * 100 if total_required > 0 else 0

        return scores

    def process_contract_hybrid(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement hybride complet : Patterns Regex + Qwen 2.5"""
        print(f"🚀 TRAITEMENT HYBRIDE COMPLET: {pdf_name}")
        print("🔧 Méthode: Patterns Regex Ultra-précis + Qwen 2.5 Intelligence")

        try:
            # 1. Extraction OCR optimisée
            print("\n📄 Phase 1: Extraction OCR optimisée...")
            ocr_text = self.extract_text_optimized(pdf_path, pdf_name)

            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")

            print(f"📝 Texte extrait: {len(ocr_text)} caractères")

            # 2. Extraction avec patterns regex avancés
            print("\n🔍 Phase 2: Extraction patterns regex...")
            regex_results = self.extract_with_advanced_patterns(ocr_text)
            regex_count = sum(len(section.values()) for section in regex_results.values() if section)
            print(f"📊 Regex a trouvé: {regex_count} champs")

            # 3. Analyse avec Qwen 2.5
            print("\n🧠 Phase 3: Analyse Qwen 2.5...")
            qwen_results = self.extract_with_qwen_intelligence(ocr_text)
            qwen_count = sum(len(section.values()) for section in qwen_results.values() if section)
            print(f"📊 Qwen a trouvé: {qwen_count} champs")

            # 4. Fusion intelligente
            print("\n🔄 Phase 4: Fusion intelligente...")
            hybrid_results = self.merge_regex_and_qwen_results(regex_results, qwen_results)
            hybrid_count = sum(len([v for v in section.values() if v]) for section in hybrid_results.values())
            print(f"📊 Fusion finale: {hybrid_count} champs")

            # 5. Calcul des scores hybrides
            print("\n📊 Phase 5: Calcul des scores...")
            hybrid_scores = self.calculate_hybrid_scores(hybrid_results)

            # 6. Assemblage final
            final_result = {
                **hybrid_results,
                "scores_qualite": hybrid_scores,
                "extraction_details": {
                    "regex_fields_found": regex_count,
                    "qwen_fields_found": qwen_count,
                    "hybrid_fields_final": hybrid_count,
                    "improvement_over_regex": hybrid_count - regex_count,
                    "qwen_contribution": qwen_count
                },
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Hybrid Regex + Qwen 2.5",
                    "ocr_text_length": len(ocr_text),
                    "qwen_available": self.qwen_model is not None,
                    "quality_score_global": hybrid_scores.get("score_global", 0),
                    "hybrid_system": True
                }
            }

            # 7. Affichage du résumé hybride
            self.display_hybrid_summary(final_result)

            print(f"\n✅ TRAITEMENT HYBRIDE TERMINÉ avec succès!")
            return final_result

        except Exception as e:
            print(f"❌ Erreur traitement hybride {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf",
                "methode_extraction": "Hybrid Regex + Qwen 2.5 (Failed)"
            }

    def display_hybrid_summary(self, result: Dict):
        """Affichage du résumé hybride"""
        print("\n📊 === RÉSUMÉ EXTRACTION HYBRIDE ===")

        scores = result.get("scores_qualite", {})
        details = result.get("extraction_details", {})

        # Statistiques hybrides
        print(f"🎯 Score global hybride: {scores.get('score_global', 0):.1f}%")
        print(f"📋 Champs détectés: {scores.get('champs_detectes', 0)}")
        print(f"🔥 Champs obligatoires: {scores.get('champs_obligatoires_detectes', 0):.1f}%")

        # Contribution de chaque méthode
        print(f"\n🔧 CONTRIBUTION DES MÉTHODES:")
        print(f"   🔍 Regex seul: {details.get('regex_fields_found', 0)} champs")
        print(f"   🧠 Qwen seul: {details.get('qwen_fields_found', 0)} champs")
        print(f"   🔄 Fusion finale: {details.get('hybrid_fields_final', 0)} champs")
        print(f"   📈 Amélioration: +{details.get('improvement_over_regex', 0)} champs vs regex")

        # Par section
        sections = ["banque", "contrat", "client", "signatures"]
        for section in sections:
            score = scores.get(f"completude_{section}", 0)
            print(f"📊 {section.upper()}: {score:.1f}%")

def main():
    """Fonction principale hybride"""
    print("🎯 === EXTRACTEUR HYBRIDE REGEX + QWEN 2.5 ===")

    # Initialisation
    extractor = HybridQwenExtractor()

    # Vérification dossiers
    if not os.path.exists('contrat'):
        print("❌ Dossier 'contrat' non trouvé")
        return

    # Création dossiers sortie
    for folder in ['images', 'ocr_texts', 'results']:
        os.makedirs(folder, exist_ok=True)

    # Recherche PDFs
    pdf_files = [f for f in os.listdir('contrat')
                 if f.lower().endswith('.pdf') and not f.startswith('~')]

    if not pdf_files:
        print("❌ Aucun PDF trouvé")
        return

    print(f"📁 {len(pdf_files)} fichier(s) PDF trouvé(s)")

    # Traitement hybride
    total_quality = 0
    successful = 0

    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"\n{'='*80}")
        print(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        print(f"{'='*80}")

        pdf_path = os.path.join('contrat', pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]

        # Traitement hybride
        result = extractor.process_contract_hybrid(pdf_path, pdf_name)

        # Sauvegarde
        output_path = f"results/{pdf_name}_HYBRID_QWEN.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        print(f"💾 Sauvegardé: {output_path}")

        # Stats
        if "erreur" not in result:
            successful += 1
            total_quality += result.get("scores_qualite", {}).get("score_global", 0)

    # Résumé final
    print(f"\n{'='*80}")
    print("🏁 EXTRACTION HYBRIDE TERMINÉE!")
    print(f"✅ Succès: {successful}/{len(pdf_files)}")
    if successful > 0:
        avg_quality = total_quality / successful
        print(f"⭐ Qualité moyenne hybride: {avg_quality:.1f}%")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
