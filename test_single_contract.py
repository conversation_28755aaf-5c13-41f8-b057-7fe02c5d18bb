#!/usr/bin/env python3
"""
Test du système amélioré sur un seul contrat
"""

import os
import json
from detection_qwen_enhanced import QwenBTKContractExtractor

def test_single_contract():
    """Test sur un seul contrat pour validation"""
    
    # Initialisation
    extractor = QwenBTKContractExtractor()
    
    # Fichier de test
    test_file = "CDTGAR1_000307_250630_141635.pdf"
    pdf_path = os.path.join("contrat", test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    if not os.path.exists(pdf_path):
        print(f"❌ Fichier {pdf_path} non trouvé")
        return
    
    print(f"🧪 Test sur : {test_file}")
    print("="*60)
    
    # Traitement
    result = extractor.process_contract_enhanced(pdf_path, pdf_name)
    
    # Affichage détaillé des résultats
    print("\n📊 RÉSULTATS DÉTAILLÉS :")
    print("="*60)

    for section in ["informations_banque", "informations_contrat", "informations_client", "informations_signatures"]:
        section_name = section.replace("informations_", "").upper()
        print(f"\n🏷️  {section_name}:")

        section_data = result.get(section, {})
        if section_data:
            for field, value in section_data.items():
                print(f"   • {field}: {value}")
        else:
            print("   (Aucune information trouvée)")
    
    # Champs masqués
    masked = result.get("champs_masques", [])
    print(f"\n🎭 CHAMPS MASQUÉS ({len(masked)}):")
    for i, field in enumerate(masked, 1):
        print(f"   {i}. {field['champ']}: {field['masque']} (confiance: {field.get('confiance', 0):.2f})")
    
    # Scores
    scores = result.get("scores_qualite", {})
    print(f"\n⭐ SCORES DE QUALITÉ:")
    print(f"   • Global: {scores.get('score_global', 0):.1f}%")
    print(f"   • Banque: {scores.get('completude_banque', 0):.1f}%")
    print(f"   • Contrat: {scores.get('completude_contrat', 0):.1f}%")
    print(f"   • Client: {scores.get('completude_client', 0):.1f}%")
    print(f"   • Signatures: {scores.get('completude_signatures', 0):.1f}%")
    print(f"   • Champs critiques: {scores.get('champs_critiques_detectes', 0):.1f}%")
    
    # Sauvegarde
    output_path = f"results/{pdf_name}_TEST_IMPROVED.json"
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Résultat sauvegardé: {output_path}")
    
    return result

if __name__ == "__main__":
    test_single_contract()
