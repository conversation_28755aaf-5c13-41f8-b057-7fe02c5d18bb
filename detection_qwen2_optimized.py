#!/usr/bin/env python3
"""
EXTRACTEUR OPTIMISÉ AVEC QWEN2-1.5B (RAPIDE ET EFFICACE)
Version légère et rapide pour CPU avec Qwen2-1.5B-Instruct
"""

import os
import json
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple

# Imports pour Qwen2 (plus léger)
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    QWEN_AVAILABLE = True
except ImportError:
    QWEN_AVAILABLE = False
    print("⚠️ Transformers non disponible")

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

class Qwen2OptimizedExtractor:
    """Extracteur optimisé avec Qwen2-1.5B pour CPU"""
    
    def __init__(self):
        self.ocr_config = r'--oem 3 --psm 6 -l fra+eng+ara'
        
        # Champs essentiels pour performance optimale
        self.essential_fields = {
            "informations_banque": {
                "nom_banque": {"required": True, "type": "text"},
                "capital_social": {"required": True, "type": "amount"},
                "numero_rc": {"required": True, "type": "identifier"},
                "adresse": {"required": True, "type": "address"},
                "forme_juridique": {"required": True, "type": "text"},
                "telephone": {"required": False, "type": "phone"}
            },
            "informations_contrat": {
                "type_contrat": {"required": True, "type": "text"},
                "numero_contrat": {"required": True, "type": "identifier"},
                "date_edition": {"required": True, "type": "date"},
                "date_signature": {"required": False, "type": "date"},
                "montant_principal": {"required": True, "type": "amount"},
                "duree": {"required": True, "type": "duration"},
                "taux_interet": {"required": True, "type": "percentage"},
                "taux_semestriel": {"required": False, "type": "percentage"},
                "commission_gestion": {"required": True, "type": "percentage"},
                "garanties": {"required": True, "type": "text"}
            },
            "informations_client": {
                "code_client": {"required": True, "type": "identifier"},
                "nom_client": {"required": True, "type": "text"},
                "numero_cin": {"required": True, "type": "cin"},
                "adresse_client": {"required": True, "type": "address"},
                "secteur_activite": {"required": True, "type": "text"},
                "numero_rc_client": {"required": True, "type": "identifier"}
            }
        }
        
        # Initialisation Qwen2 optimisé
        self.qwen_model = None
        self.qwen_tokenizer = None
        self.init_qwen2_fast()

    def init_qwen2_fast(self):
        """Initialise Qwen2-1.5B optimisé pour vitesse"""
        if not QWEN_AVAILABLE:
            print("⚠️ Qwen non disponible - utilisation patterns seuls")
            return
            
        try:
            print("🚀 Chargement Qwen2-1.5B (rapide et léger)...")
            
            # Modèle Qwen2 plus rapide
            model_name = "Qwen/Qwen2-1.5B-Instruct"
            
            print("⚡ Optimisations CPU activées...")
            
            # Tokenizer rapide
            self.qwen_tokenizer = AutoTokenizer.from_pretrained(
                model_name, 
                trust_remote_code=True,
                use_fast=True
            )
            
            # Modèle optimisé pour vitesse
            self.qwen_model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float32,  # Optimal pour CPU
                device_map=None,
                trust_remote_code=True,
                low_cpu_mem_usage=True,
                use_cache=True
            )
            
            # Optimisations CPU
            self.qwen_model = self.qwen_model.to("cpu")
            self.qwen_model.eval()
            
            # Configuration PyTorch pour performance
            torch.set_num_threads(6)  # Plus de threads pour vitesse
            torch.set_num_interop_threads(2)
            
            print("✅ Qwen2-1.5B chargé et optimisé pour vitesse!")
            
        except Exception as e:
            print(f"❌ Erreur chargement Qwen2: {str(e)}")
            self.qwen_model = None
            self.qwen_tokenizer = None

    def enhance_image_fast(self, image: Image.Image) -> Image.Image:
        """Amélioration d'image rapide"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Amélioration rapide
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(img_array)
        
        # Binarisation simple et rapide
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return Image.fromarray(binary)

    def extract_text_fast(self, pdf_path: str, pdf_name: str) -> str:
        """Extraction OCR rapide"""
        print(f"📄 Extraction OCR rapide: {pdf_name}")
        
        # Créer dossiers
        os.makedirs('ocr_texts', exist_ok=True)
        
        try:
            # DPI réduit pour vitesse
            pages = convert_from_path(pdf_path, dpi=200, fmt='png')
        except Exception as e:
            print(f"❌ Erreur conversion PDF: {str(e)}")
            return ""
        
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            print(f"🔍 Page {page_num}...")
            
            try:
                # Amélioration rapide
                enhanced_page = self.enhance_image_fast(page)
                
                # OCR rapide
                text = pytesseract.image_to_string(enhanced_page, config=self.ocr_config)
                all_text += f"\n--- Page {page_num} ---\n{text}"
                
                print(f"✅ Page {page_num} OK")
            except Exception as e:
                print(f"❌ Erreur page {page_num}: {str(e)}")
        
        # Sauvegarde
        text_path = f"ocr_texts/{pdf_name}_qwen2.txt"
        try:
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(all_text)
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {str(e)}")
        
        return all_text

    def extract_with_patterns_fast(self, text: str) -> Dict:
        """Extraction patterns rapide"""
        print("🔍 Extraction patterns rapide...")
        
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {}
        }
        
        clean_text = re.sub(r'\s+', ' ', text)
        
        # Patterns essentiels optimisés
        patterns = {
            "informations_banque": {
                "nom_banque": [r"BANQUE\s+TUNISO[\-\s]*KOWEITIENNE", r"B\.T\.K\.?"],
                "capital_social": [r"capital\s+de\s+([\d\s,\.]+)\s*dinars?"],
                "numero_rc": [r"R\.?C\.?\s*[=:]\s*([A-Z]?\d+)"],
                "adresse": [r"siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)"],
                "forme_juridique": [r"(société\s+anonyme)", r"(S\.A\.?)"],
                "telephone": [r"(?:Tél|Tel)\s*[:\.]?\s*([\d\s\-\+\(\)]{8,})"]
            },
            "informations_contrat": {
                "type_contrat": [r"CONTRAT\s+DE\s+(PRET|PRÊT)"],
                "numero_contrat": [r"Numéro\s+(?:de\s+)?(?:contrat|prêt)\s*[=:]\s*([A-Z0-9\-\/]+)"],
                "date_edition": [r"Edité\s+le\s*:?\s*(\d{1,2}\/\d{1,2}\/\d{4})"],
                "montant_principal": [r"somme\s+principale\s+(?:de\s+)?([\d\s,\.]+)"],
                "duree": [r"durée\s+de\s+(\d+\s+mois)"],
                "taux_interet": [r"taux\s+d['\']intérêt\s*[=:]\s*([\d,\.]+\s*%?)"],
                "commission_gestion": [r"commission\s+de\s+gestion\s+de\s+([\d,\.]+\s*%)"],
                "garanties": [r"garanties?\s*[=:]\s*([^\.]+)"]
            },
            "informations_client": {
                "code_client": [r"Code\s+Client\s*[=:]\s*([A-Z0-9X]{4,})"],
                "nom_client": [r"Et\s+([A-Z][A-Z\s&\-\.]+),?\s+dont\s+le\s+siège"],
                "numero_cin": [r"CIN\s*[=:]\s*(\d{8})"],
                "adresse_client": [r"dont\s+le\s+siège\s+social\s+est\s+(?:à\s+)?([^,\n\.]+)"],
                "secteur_activite": [r"Secteur\s+d['\']activité\s*[=:]\s*([^,\n\.]+)"],
                "numero_rc_client": [r"immatriculé(?:e)?\s+au.*?n°\s*([A-Z0-9]+)"]
            }
        }
        
        # Application rapide des patterns
        for section, section_patterns in patterns.items():
            for field, field_patterns in section_patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, clean_text, re.IGNORECASE)
                    if match:
                        value = match.group(1).strip() if match.groups() else match.group(0).strip()
                        if value and not result[section].get(field):
                            result[section][field] = value
                            break
        
        return result

    def extract_with_qwen2_fast(self, text: str) -> Dict:
        """Extraction Qwen2 rapide et efficace"""
        if not self.qwen_model or not self.qwen_tokenizer:
            print("⚠️ Qwen2 non disponible")
            return {}
            
        print("🧠 Analyse Qwen2 rapide...")
        
        try:
            # Prompt optimisé pour vitesse et précision
            prompt = f"""<|im_start|>system
Tu es un expert en contrats BTK. Extrait rapidement les informations essentielles au format JSON.
<|im_end|>

<|im_start|>user
Extrait ces informations du contrat BTK au format JSON :

{{
  "informations_banque": {{
    "nom_banque": "",
    "capital_social": "",
    "numero_rc": "",
    "adresse": "",
    "forme_juridique": "",
    "telephone": ""
  }},
  "informations_contrat": {{
    "type_contrat": "",
    "numero_contrat": "",
    "date_edition": "",
    "montant_principal": "",
    "duree": "",
    "taux_interet": "",
    "commission_gestion": "",
    "garanties": ""
  }},
  "informations_client": {{
    "code_client": "",
    "nom_client": "",
    "numero_cin": "",
    "adresse_client": "",
    "secteur_activite": "",
    "numero_rc_client": ""
  }}
}}

RÈGLES :
- JSON valide uniquement
- Dates format JJ/MM/AAAA
- CIN 8 chiffres
- Conserver xxx, XXXX si masqué

TEXTE :
{text[:6000]}
<|im_end|>

<|im_start|>assistant
```json"""

            # Tokenisation rapide
            inputs = self.qwen_tokenizer(
                prompt, 
                return_tensors="pt", 
                max_length=3072,  # Contexte réduit pour vitesse
                truncation=True,
                padding=False
            )
            
            # Génération rapide
            with torch.no_grad():
                outputs = self.qwen_model.generate(
                    inputs.input_ids,
                    max_new_tokens=800,   # Limité pour vitesse
                    temperature=0.1,      # Basse pour précision
                    do_sample=False,      # Greedy pour vitesse
                    pad_token_id=self.qwen_tokenizer.eos_token_id,
                    eos_token_id=self.qwen_tokenizer.eos_token_id,
                    use_cache=True
                )
            
            # Décodage rapide
            response = self.qwen_tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:], 
                skip_special_tokens=True
            )
            
            # Extraction JSON
            json_match = re.search(r'\{[\s\S]*\}', response)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    print("✅ Qwen2 analyse réussie")
                    return result
                except json.JSONDecodeError:
                    print("❌ Erreur JSON Qwen2")
                    
        except Exception as e:
            print(f"❌ Erreur Qwen2: {str(e)}")
        
        return {}

    def merge_results_fast(self, patterns_result: Dict, qwen_result: Dict) -> Dict:
        """Fusion rapide des résultats"""
        print("🔄 Fusion rapide des résultats...")

        merged = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {}
        }

        for section in merged.keys():
            # Base : résultats patterns
            merged[section] = patterns_result.get(section, {}).copy()

            # Enrichissement : résultats Qwen2
            qwen_section = qwen_result.get(section, {})
            for field, qwen_value in qwen_section.items():
                if qwen_value and str(qwen_value).strip():
                    current_value = merged[section].get(field, "")

                    # Utiliser Qwen2 si pas de valeur patterns ou si Qwen2 est plus complet
                    if not current_value or len(str(qwen_value)) > len(str(current_value)):
                        merged[section][field] = qwen_value

        return merged

    def calculate_scores_fast(self, result: Dict) -> Dict:
        """Calcul rapide des scores"""
        scores = {
            "score_global": 0,
            "completude_banque": 0,
            "completude_contrat": 0,
            "completude_client": 0,
            "champs_detectes": 0,
            "champs_obligatoires": 0
        }

        total_detected = 0
        total_possible = 0
        total_required_detected = 0
        total_required = 0

        for section, fields in self.essential_fields.items():
            section_key = f"completude_{section.split('_')[1]}"

            detected = 0
            required_detected = 0
            required_count = 0

            for field, field_info in fields.items():
                is_required = field_info.get("required", False)
                has_value = bool(result.get(section, {}).get(field))

                if has_value:
                    detected += 1
                    total_detected += 1

                    if is_required:
                        required_detected += 1
                        total_required_detected += 1

                if is_required:
                    required_count += 1
                    total_required += 1

                total_possible += 1

            scores[section_key] = (detected / len(fields)) * 100 if fields else 0

        scores["score_global"] = (total_detected / total_possible) * 100 if total_possible > 0 else 0
        scores["champs_detectes"] = total_detected
        scores["champs_obligatoires"] = (total_required_detected / total_required) * 100 if total_required > 0 else 0

        return scores

    def process_contract_qwen2(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement complet avec Qwen2 optimisé"""
        print(f"🚀 TRAITEMENT QWEN2 OPTIMISÉ: {pdf_name}")

        try:
            # 1. Extraction OCR rapide
            print("\n📄 Phase 1: OCR rapide...")
            start_ocr = datetime.now()
            ocr_text = self.extract_text_fast(pdf_path, pdf_name)
            ocr_time = (datetime.now() - start_ocr).total_seconds()

            if not ocr_text.strip():
                raise Exception("Aucun texte extrait")

            print(f"📝 OCR terminé en {ocr_time:.1f}s - {len(ocr_text)} caractères")

            # 2. Extraction patterns rapide
            print("\n🔍 Phase 2: Patterns rapides...")
            start_patterns = datetime.now()
            patterns_results = self.extract_with_patterns_fast(ocr_text)
            patterns_time = (datetime.now() - start_patterns).total_seconds()
            patterns_count = sum(len([v for v in section.values() if v]) for section in patterns_results.values())
            print(f"🔍 Patterns terminés en {patterns_time:.1f}s - {patterns_count} champs")

            # 3. Analyse Qwen2 rapide
            print("\n🧠 Phase 3: Qwen2 rapide...")
            start_qwen = datetime.now()
            qwen_results = self.extract_with_qwen2_fast(ocr_text)
            qwen_time = (datetime.now() - start_qwen).total_seconds()
            qwen_count = sum(len([v for v in section.values() if v]) for section in qwen_results.values())
            print(f"🧠 Qwen2 terminé en {qwen_time:.1f}s - {qwen_count} champs")

            # 4. Fusion rapide
            print("\n🔄 Phase 4: Fusion...")
            start_merge = datetime.now()
            final_results = self.merge_results_fast(patterns_results, qwen_results)
            merge_time = (datetime.now() - start_merge).total_seconds()
            final_count = sum(len([v for v in section.values() if v]) for section in final_results.values())
            print(f"🔄 Fusion terminée en {merge_time:.1f}s - {final_count} champs")

            # 5. Scores rapides
            scores = self.calculate_scores_fast(final_results)

            # 6. Assemblage final
            total_time = ocr_time + patterns_time + qwen_time + merge_time

            final_result = {
                **final_results,
                "scores_qualite": scores,
                "performance": {
                    "temps_ocr": ocr_time,
                    "temps_patterns": patterns_time,
                    "temps_qwen2": qwen_time,
                    "temps_fusion": merge_time,
                    "temps_total": total_time,
                    "champs_patterns": patterns_count,
                    "champs_qwen2": qwen_count,
                    "champs_final": final_count,
                    "amelioration": final_count - patterns_count
                },
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Qwen2-1.5B Optimized",
                    "ocr_text_length": len(ocr_text),
                    "qwen2_available": self.qwen_model is not None,
                    "quality_score_global": scores.get("score_global", 0)
                }
            }

            # 7. Affichage résumé
            self.display_summary_fast(final_result)

            print(f"\n✅ TRAITEMENT QWEN2 TERMINÉ en {total_time:.1f}s!")
            return final_result

        except Exception as e:
            print(f"❌ Erreur traitement Qwen2 {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf"
            }

    def display_summary_fast(self, result: Dict):
        """Affichage rapide du résumé"""
        print("\n📊 === RÉSUMÉ QWEN2 OPTIMISÉ ===")

        scores = result.get("scores_qualite", {})
        perf = result.get("performance", {})

        print(f"🎯 Score global: {scores.get('score_global', 0):.1f}%")
        print(f"📋 Champs détectés: {scores.get('champs_detectes', 0)}")
        print(f"🔥 Champs obligatoires: {scores.get('champs_obligatoires', 0):.1f}%")
        print(f"⚡ Temps total: {perf.get('temps_total', 0):.1f}s")
        print(f"📈 Amélioration vs patterns: +{perf.get('amelioration', 0)} champs")
